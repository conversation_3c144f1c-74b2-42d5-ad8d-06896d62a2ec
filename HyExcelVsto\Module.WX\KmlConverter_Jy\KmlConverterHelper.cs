using System;
using System.IO;
using System.Windows.Forms;
using ET;
using ExtensionsTools;

namespace HyExcelVsto.Module.WX.KmlConverter
{
    /// <summary>
    /// KML转换器帮助类 提供简单易用的KML转换功能接口
    /// </summary>
    public static class KmlConverterHelper
    {
        /// <summary>
        /// 转换KML文件，为地标添加备注信息
        /// </summary>
        /// <param name="sourceKmlPath">源KML文件路径</param>
        /// <param name="targetKmlPath">目标KML文件路径</param>
        /// <returns>转换是否成功</returns>
        public static bool ConvertKml(string sourceKmlPath, string targetKmlPath)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(sourceKmlPath))
                {
                    ETLogManager.Error("源KML文件路径不能为空");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(targetKmlPath))
                {
                    ETLogManager.Error("目标KML文件路径不能为空");
                    return false;
                }

                // 执行转换
                return KmlDescriptionConverter.ConvertKmlWithDescription(sourceKmlPath, targetKmlPath);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"KML转换失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 通过文件对话框选择KML文件并转换
        /// </summary>
        /// <returns>转换是否成功</returns>
        public static bool ConvertKmlWithDialog()
        {
            try
            {
                // 选择源文件
                using (OpenFileDialog openDialog = new OpenFileDialog())
                {
                    openDialog.Title = "选择要转换的KML文件";
                    openDialog.Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*";
                    openDialog.FilterIndex = 1;

                    if (openDialog.ShowDialog() != DialogResult.OK)
                    {
                        return false;
                    }

                    string sourceFile = openDialog.FileName;

                    // 选择目标文件
                    using (SaveFileDialog saveDialog = new SaveFileDialog())
                    {
                        saveDialog.Title = "保存转换后的KML文件";
                        saveDialog.Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*";
                        saveDialog.FilterIndex = 1;

                        // 默认文件名：在原文件名后添加"_converted"
                        string fileName = Path.GetFileNameWithoutExtension(sourceFile);
                        string extension = Path.GetExtension(sourceFile);
                        saveDialog.FileName = $"{fileName}_converted{extension}";

                        if (saveDialog.ShowDialog() != DialogResult.OK)
                        {
                            return false;
                        }

                        string targetFile = saveDialog.FileName;

                        // 执行转换
                        bool success = ConvertKml(sourceFile, targetFile);

                        if (success)
                        {
                            MessageBox.Show($"KML文件转换成功！\n\n源文件：{sourceFile}\n目标文件：{targetFile}",
                                          "转换成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("KML文件转换失败，请查看日志了解详细信息。",
                                          "转换失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }

                        return success;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"KML转换对话框操作失败: {ex.Message}", ex);
                MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 批量转换指定目录下的所有KML文件
        /// </summary>
        /// <param name="sourceDirectory">源目录路径</param>
        /// <param name="targetDirectory">目标目录路径</param>
        /// <param name="addSuffix">是否在文件名后添加后缀</param>
        /// <returns>成功转换的文件数量</returns>
        public static int BatchConvertKml(string sourceDirectory, string targetDirectory, bool addSuffix = true)
        {
            int successCount = 0;

            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(sourceDirectory) || !Directory.Exists(sourceDirectory))
                {
                    ETLogManager.Error($"源目录不存在: {sourceDirectory}");
                    return 0;
                }

                if (string.IsNullOrWhiteSpace(targetDirectory))
                {
                    ETLogManager.Error("目标目录路径不能为空");
                    return 0;
                }

                // 确保目标目录存在
                if (!Directory.Exists(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                }

                // 获取所有KML文件
                string[] kmlFiles = Directory.GetFiles(sourceDirectory, "*.kml", SearchOption.TopDirectoryOnly);

                ETLogManager.Info($"找到 {kmlFiles.Length} 个KML文件待转换");

                foreach (string sourceFile in kmlFiles)
                {
                    try
                    {
                        // 生成目标文件路径
                        string fileName = Path.GetFileNameWithoutExtension(sourceFile);
                        string extension = Path.GetExtension(sourceFile);

                        string targetFileName = addSuffix ? $"{fileName}_converted{extension}" : $"{fileName}{extension}";
                        string targetFile = Path.Combine(targetDirectory, targetFileName);

                        // 执行转换
                        if (ConvertKml(sourceFile, targetFile))
                        {
                            successCount++;
                            ETLogManager.Info($"成功转换: {Path.GetFileName(sourceFile)}");
                        }
                        else
                        {
                            ETLogManager.Warning($"转换失败: {Path.GetFileName(sourceFile)}");
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error($"转换文件 {Path.GetFileName(sourceFile)} 时发生错误: {ex.Message}", ex);
                    }
                }

                ETLogManager.Info($"批量转换完成，成功转换 {successCount}/{kmlFiles.Length} 个文件");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"批量转换KML文件失败: {ex.Message}", ex);
            }

            return successCount;
        }

        /// <summary>
        /// 验证KML文件格式
        /// </summary>
        /// <param name="kmlFilePath">KML文件路径</param>
        /// <returns>文件是否有效</returns>
        public static bool ValidateKmlFile(string kmlFilePath)
        {
            try
            {
                if (!File.Exists(kmlFilePath))
                {
                    ETLogManager.Error($"文件不存在: {kmlFilePath}");
                    return false;
                }

                // 检查文件扩展名
                string extension = Path.GetExtension(kmlFilePath).ToLower();
                if (extension != ".kml")
                {
                    ETLogManager.Warning($"文件扩展名不是.kml: {kmlFilePath}");
                }

                // 尝试读取文件内容的前几行，检查是否包含KML标识
                using (StreamReader reader = new StreamReader(kmlFilePath))
                {
                    string content = reader.ReadToEnd();

                    if (string.IsNullOrWhiteSpace(content))
                    {
                        ETLogManager.Error("KML文件内容为空");
                        return false;
                    }

                    // 检查是否包含KML相关标签
                    if (!content.Contains("kml") && !content.Contains("Placemark"))
                    {
                        ETLogManager.Warning("文件可能不是有效的KML格式");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"验证KML文件时发生错误: {ex.Message}", ex);
                return false;
            }
        }
    }
}