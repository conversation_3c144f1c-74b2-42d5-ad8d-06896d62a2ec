using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Linq;
using ET;
using ExtensionsTools;

namespace HyExcelVsto.Module.WX.KmlConverter
{
    /// <summary>
    /// KML文件描述信息转换器 为KML文件中的地标添加备注信息
    /// </summary>
    public class KmlDescriptionConverter
    {
        /// <summary>
        /// 转换KML文件，为每个地标添加备注信息
        /// </summary>
        /// <param name="sourceKmlPath">源KML文件路径</param>
        /// <param name="targetKmlPath">目标KML文件路径</param>
        /// <returns>转换是否成功</returns>
        public static bool ConvertKmlWithDescription(string sourceKmlPath, string targetKmlPath)
        {
            try
            {
                ETLogManager.Info($"开始转换KML文件: {sourceKmlPath} -> {targetKmlPath}");

                // 检查源文件是否存在
                if (!File.Exists(sourceKmlPath))
                {
                    ETLogManager.Error($"源KML文件不存在: {sourceKmlPath}");
                    return false;
                }

                // 确保目标目录存在
                string targetDir = Path.GetDirectoryName(targetKmlPath);
                if (!string.IsNullOrEmpty(targetDir) && !Directory.Exists(targetDir))
                {
                    Directory.CreateDirectory(targetDir);
                }

                // 读取并解析KML文件
                XDocument kmlDoc = LoadKmlDocument(sourceKmlPath);
                if (kmlDoc == null)
                {
                    ETLogManager.Error("无法加载KML文档");
                    return false;
                }

                // 处理所有Placemark元素
                int processedCount = ProcessPlacemarks(kmlDoc);

                // 保存转换后的KML文件
                SaveKmlDocument(kmlDoc, targetKmlPath);

                ETLogManager.Info($"KML转换完成，共处理 {processedCount} 个地标");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"转换KML文件时发生错误: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 加载KML文档，支持多种编码格式
        /// </summary>
        /// <param name="kmlFilePath">KML文件路径</param>
        /// <returns>XDocument对象</returns>
        private static XDocument LoadKmlDocument(string kmlFilePath)
        {
            try
            {
                // 尝试不同的编码格式加载KML文件
                Encoding[] encodings = {
                    Encoding.UTF8,
                    Encoding.GetEncoding("GBK"),
                    Encoding.GetEncoding("GB2312"),
                    Encoding.GetEncoding("GB18030"),
                    Encoding.Default
                };

                foreach (Encoding encoding in encodings)
                {
                    try
                    {
                        // 使用指定编码读取文件内容
                        string xmlContent;
                        using (StreamReader reader = new StreamReader(kmlFilePath, encoding, true))
                        {
                            xmlContent = reader.ReadToEnd();
                        }

                        // 预处理XML内容
                        xmlContent = PreprocessXmlContent(xmlContent);

                        // 尝试解析为XDocument
                        XDocument kmlDoc = XDocument.Parse(xmlContent);
                        ETLogManager.Info($"成功使用 {encoding.EncodingName} 编码加载KML文件");
                        return kmlDoc;
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Debug($"尝试使用 {encoding.EncodingName} 编码加载失败: {ex.Message}");
                        // 继续尝试下一种编码
                    }
                }

                ETLogManager.Error("无法使用任何编码格式加载KML文件");
                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"加载KML文档时发生错误: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 预处理XML内容，解决常见的XML解析问题
        /// </summary>
        /// <param name="xmlContent">原始XML内容</param>
        /// <returns>预处理后的XML内容</returns>
        private static string PreprocessXmlContent(string xmlContent)
        {
            if (string.IsNullOrEmpty(xmlContent))
                return xmlContent;

            // 替换可能导致解析错误的特殊字符
            xmlContent = xmlContent.Replace("\uFFFD", string.Empty);

            // 替换ASCII扩展字符集的非法XML字符
            xmlContent = Regex.Replace(xmlContent, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F]", string.Empty);

            // 处理XML声明问题
            if (!xmlContent.TrimStart().StartsWith("<?xml"))
            {
                xmlContent = $"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n{xmlContent}";
            }

            return xmlContent;
        }

        /// <summary>
        /// 处理所有Placemark元素，添加描述信息
        /// </summary>
        /// <param name="kmlDoc">KML文档</param>
        /// <returns>处理的地标数量</returns>
        private static int ProcessPlacemarks(XDocument kmlDoc)
        {
            int processedCount = 0;

            try
            {
                // 查找所有Placemark元素（不考虑命名空间）
                var placemarks = kmlDoc.Descendants().Where(e => e.Name.LocalName == "Placemark");

                foreach (XElement placemark in placemarks)
                {
                    try
                    {
                        // 获取地标名称（支持name和n标签）
                        XElement nameElement = placemark.Descendants().FirstOrDefault(e => e.Name.LocalName == "name" || e.Name.LocalName == "n");
                        string stationName = nameElement?.Value?.Trim() ?? "";

                        if (string.IsNullOrEmpty(stationName))
                        {
                            ETLogManager.Debug("跳过没有名称的地标");
                            continue;
                        }

                        // 获取坐标信息
                        XElement coordinatesElement = placemark.Descendants().FirstOrDefault(e => e.Name.LocalName == "coordinates");
                        string coordinates = coordinatesElement?.Value?.Trim() ?? "";

                        if (string.IsNullOrEmpty(coordinates))
                        {
                            ETLogManager.Debug($"地标 {stationName} 没有坐标信息");
                            continue;
                        }

                        // 解析经纬度
                        string[] coordParts = coordinates.Split(',');
                        if (coordParts.Length < 2)
                        {
                            ETLogManager.Debug($"地标 {stationName} 坐标格式不正确: {coordinates}");
                            continue;
                        }

                        string longitude = coordParts[0].Trim();
                        string latitude = coordParts[1].Trim();

                        // 生成描述信息
                        string description = GenerateDescription(stationName, longitude, latitude);

                        // 更新或创建description元素
                        UpdateDescription(placemark, description);

                        processedCount++;
                        ETLogManager.Debug($"已处理地标: {stationName}");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error($"处理地标时发生错误: {ex.Message}", ex);
                        // 继续处理下一个地标
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"处理Placemark元素时发生错误: {ex.Message}", ex);
            }

            return processedCount;
        }

        /// <summary>
        /// 生成地标的描述信息
        /// </summary>
        /// <param name="stationName">地标名称</param>
        /// <param name="longitude">经度</param>
        /// <param name="latitude">纬度</param>
        /// <returns>描述信息</returns>
        private static string GenerateDescription(string stationName, string longitude, string latitude)
        {
            // 第1行：地标名称
            string line1 = stationName;

            // 第2行：经纬度信息（用半角逗号连接）
            string line2 = $"{longitude},{latitude}";

            // 第3行：地标名第一个横杠前面的名字
            string line3 = ExtractPrefixBeforeFirstDash(stationName);

            // 组合成描述信息
            return $"{line1}\n{line2}\n{line3}";
        }

        /// <summary>
        /// 提取地标名第一个横杠前面的名字
        /// </summary>
        /// <param name="stationName">地标名称</param>
        /// <returns>第一个横杠前的名字</returns>
        private static string ExtractPrefixBeforeFirstDash(string stationName)
        {
            if (string.IsNullOrEmpty(stationName))
                return "";

            // 查找第一个横杠的位置
            int dashIndex = stationName.IndexOf('-');
            if (dashIndex > 0)
            {
                return stationName.Substring(0, dashIndex).Trim();
            }

            // 如果没有横杠，返回原名称
            return stationName;
        }

        /// <summary>
        /// 更新或创建地标的description元素
        /// </summary>
        /// <param name="placemark">地标元素</param>
        /// <param name="description">描述内容</param>
        private static void UpdateDescription(XElement placemark, string description)
        {
            try
            {
                // 查找现有的description元素
                XElement descElement = placemark.Descendants().FirstOrDefault(e => e.Name.LocalName == "description");

                if (descElement != null)
                {
                    // 更新现有的description元素
                    descElement.Value = description;
                }
                else
                {
                    // 创建新的description元素
                    XNamespace ns = placemark.Name.Namespace;
                    XElement newDescElement = new XElement(ns + "description", description);

                    // 将description元素插入到name或n元素之后
                    XElement nameElement = placemark.Descendants().FirstOrDefault(e => e.Name.LocalName == "name" || e.Name.LocalName == "n");
                    if (nameElement != null)
                    {
                        nameElement.AddAfterSelf(newDescElement);
                    }
                    else
                    {
                        // 如果没有name或n元素，插入到placemark的开头
                        placemark.AddFirst(newDescElement);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"更新description元素时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存KML文档到文件
        /// </summary>
        /// <param name="kmlDoc">KML文档</param>
        /// <param name="targetPath">目标文件路径</param>
        private static void SaveKmlDocument(XDocument kmlDoc, string targetPath)
        {
            try
            {
                // 使用UTF-8编码保存文件
                using (StreamWriter writer = new StreamWriter(targetPath, false, Encoding.UTF8))
                {
                    kmlDoc.Save(writer);
                }

                ETLogManager.Info($"KML文件已保存到: {targetPath}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存KML文件时发生错误: {ex.Message}", ex);
                throw;
            }
        }
    }
}