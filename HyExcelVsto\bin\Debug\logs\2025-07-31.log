﻿2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:24:17 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:24:17 [INFO] OpenForm: 保持窗体现有标题 '通过经纬度查找'
2025-07-31 15:24:17 [INFO] OpenForm: 准备打开窗体 '通过经纬度查找'，位置: Center，单实例: False
2025-07-31 15:24:17 [INFO] 开始显示窗体 '通过经纬度查找'，位置模式: Center
2025-07-31 15:24:17 [INFO] 窗体 '通过经纬度查找' 以TopMostForm为父窗体显示
2025-07-31 15:24:17 [INFO] 窗体 '通过经纬度查找' 显示完成，句柄: 6689870
2025-07-31 15:24:17 [INFO] OpenForm: 窗体 '通过经纬度查找' 打开成功
2025-07-31 15:24:22 [INFO] OpenForm: 保持窗体现有标题 'KML转换器 - 添加备注信息'
2025-07-31 15:24:22 [INFO] OpenForm: 准备打开窗体 'KML转换器 - 添加备注信息'，位置: Center，单实例: True
2025-07-31 15:24:22 [INFO] 开始显示窗体 'KML转换器 - 添加备注信息'，位置模式: Center
2025-07-31 15:24:23 [INFO] 窗体 'KML转换器 - 添加备注信息' 以TopMostForm为父窗体显示
2025-07-31 15:24:23 [INFO] 窗体 'KML转换器 - 添加备注信息' 显示完成，句柄: 3482042
2025-07-31 15:24:23 [INFO] OpenForm: 窗体 'KML转换器 - 添加备注信息' 打开成功
2025-07-31 15:25:16 [INFO] Excel窗口句柄监控器初始化完成
2025-07-31 15:25:16 [INFO] 配置文件实例已在加载时初始化
2025-07-31 15:25:16 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-31 15:25:16 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-31 15:25:16 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-31 15:25:16 [INFO] 成功初始化Excel应用程序实例
2025-07-31 15:25:16 [INFO] 自动备份路径未配置
2025-07-31 15:25:16 [DEBUG] 开始初始化授权控制器
2025-07-31 15:25:17 [DEBUG] 授权系统初始化完成，耗时: 504ms
2025-07-31 15:25:17 [DEBUG] 开始初始化授权验证
2025-07-31 15:25:17 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-31 15:25:17 [DEBUG] 权限管理器初始化成功
2025-07-31 15:25:17 [DEBUG] 使用新的权限管理器进行初始化
2025-07-31 15:25:17 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-31 15:25:17 [INFO] 开始初始化UI权限管理
2025-07-31 15:25:17 [DEBUG] [实例ID: 42dde3a8] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-31 15:25:17 [DEBUG] 🔍 [实例ID: 42dde3a8] 字典引用一致性检查:
2025-07-31 15:25:17 [DEBUG] 🔍   标题映射一致性: True
2025-07-31 15:25:17 [DEBUG] 🔍   权限映射一致性: True
2025-07-31 15:25:17 [DEBUG] 🔍   信息映射一致性: True
2025-07-31 15:25:17 [DEBUG] 🔍   特殊控件一致性: True
2025-07-31 15:25:17 [DEBUG] 控件权限管理器初始化完成 [实例ID: 42dde3a8]
2025-07-31 15:25:17 [DEBUG] 开始注册控件权限映射
2025-07-31 15:25:17 [INFO] 开始初始化全局控件映射
2025-07-31 15:25:17 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-31 15:25:17 [DEBUG] 开始生成控件标题映射
2025-07-31 15:25:17 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-31 15:25:17 [DEBUG] 通过反射获取到 122 个字段
2025-07-31 15:25:17 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-31 15:25:17 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-31 15:25:17 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-31 15:25:17 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 控件结构获取完成，共获取到 120 个控件
2025-07-31 15:25:17 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-31 15:25:17 [INFO] 控件标题映射生成完成，共生成 107 项映射
2025-07-31 15:25:17 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-31 15:25:17 [DEBUG] 全局控件标题映射生成完成，共生成 107 项
2025-07-31 15:25:17 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-31 15:25:17 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-31 15:25:17 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-31 15:25:17 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-31 15:25:17 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-31 15:25:17 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-31 15:25:17 [INFO] === znAbout控件标题映射诊断 ===
2025-07-31 15:25:17 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-31 15:25:17 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-31 15:25:17 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-31 15:25:17 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-31 15:25:17 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-31 15:25:17 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-07-31 15:25:17 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-07-31 15:25:17 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-07-31 15:25:17 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-31 15:25:17 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-31 15:25:17 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-31 15:25:17 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-31 15:25:17 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-31 15:25:17 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-07-31 15:25:17 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-31 15:25:17 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-31 15:25:17 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-31 15:25:17 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-31 15:25:17 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-31 15:25:17 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-31 15:25:17 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-31 15:25:17 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-31 15:25:17 [DEBUG] 控件映射: button3 -> '关于'
2025-07-31 15:25:17 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-31 15:25:17 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-31 15:25:17 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-31 15:25:17 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-31 15:25:17 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-31 15:25:17 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-31 15:25:17 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-31 15:25:17 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-31 15:25:17 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-31 15:25:17 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-31 15:25:17 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-31 15:25:17 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-31 15:25:17 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-31 15:25:17 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-31 15:25:17 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-31 15:25:17 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-31 15:25:17 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-31 15:25:17 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-31 15:25:17 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-31 15:25:17 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-07-31 15:25:17 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-31 15:25:17 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-31 15:25:17 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-31 15:25:17 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-07-31 15:25:17 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-31 15:25:17 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-31 15:25:17 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-31 15:25:17 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-31 15:25:17 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-31 15:25:17 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-31 15:25:17 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-31 15:25:17 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-31 15:25:17 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-31 15:25:17 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-31 15:25:17 [DEBUG] 控件映射: group1 -> '关于'
2025-07-31 15:25:17 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-31 15:25:17 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-31 15:25:17 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-31 15:25:17 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-31 15:25:17 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-31 15:25:17 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-31 15:25:17 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-31 15:25:17 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-31 15:25:17 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-31 15:25:17 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-07-31 15:25:17 [DEBUG] 控件映射: menu2 -> '其它'
2025-07-31 15:25:17 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-31 15:25:17 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-31 15:25:17 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-31 15:25:17 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-31 15:25:17 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-31 15:25:17 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-31 15:25:17 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-31 15:25:17 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-31 15:25:17 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-31 15:25:17 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-31 15:25:17 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-31 15:25:17 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-31 15:25:17 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-31 15:25:17 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-31 15:25:17 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-31 15:25:17 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-31 15:25:17 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-31 15:25:17 [DEBUG] 开始生成控件权限映射
2025-07-31 15:25:17 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-31 15:25:17 [DEBUG] 通过反射获取到 122 个字段
2025-07-31 15:25:17 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-31 15:25:17 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-31 15:25:17 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-31 15:25:17 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-31 15:25:17 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-31 15:25:17 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:25:17 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:25:17 [INFO] 控件结构获取完成，共获取到 120 个控件
2025-07-31 15:25:17 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-31 15:25:17 [INFO] 控件权限映射生成完成，共生成 114 项映射
2025-07-31 15:25:17 [DEBUG] 全局控件权限映射生成完成，共生成 114 项
2025-07-31 15:25:17 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-31 15:25:17 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-31 15:25:17 [INFO] 全局控件映射初始化完成 - 标题映射: 107 项, 权限映射: 114 项
2025-07-31 15:25:17 [DEBUG] 批量注册控件权限映射完成，成功: 114/114
2025-07-31 15:25:17 [DEBUG] HyExcel控件权限映射注册完成，共注册 114 个控件
2025-07-31 15:25:17 [INFO] 开始初始化权限验证
2025-07-31 15:25:17 [DEBUG] 设置默认UI可见性为false
2025-07-31 15:25:17 [DEBUG] 开始检查所有需要的权限
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [INFO] 启动网络授权信息获取任务
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [INFO] 所有权限检查完成
2025-07-31 15:25:17 [DEBUG] 应用权限状态到UI控件
2025-07-31 15:25:17 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: False
2025-07-31 15:25:17 [DEBUG] 🔍 执行User权限UI操作 - 用户无User权限，不修改znAbout控件状态
2025-07-31 15:25:17 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: False
2025-07-31 15:25:17 [DEBUG] 🔍 执行Develop权限UI操作 - 用户无Develop权限，不修改znAbout控件状态
2025-07-31 15:25:17 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:25:17 [DEBUG] 启动后台权限刷新任务
2025-07-31 15:25:17 [DEBUG] 启动延迟权限刷新任务
2025-07-31 15:25:17 [INFO] 权限验证初始化完成
2025-07-31 15:25:17 [INFO] UI权限管理初始化完成
2025-07-31 15:25:17 [INFO] 收到权限管理器初始化完成通知
2025-07-31 15:25:17 [INFO] 开始刷新控件标题
2025-07-31 15:25:17 [DEBUG] 开始刷新所有控件权限状态
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:25:17 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\remote_license_cache.dat
2025-07-31 15:25:17 [WARN] 未从本地文件获取到有效的授权信息
2025-07-31 15:25:17 [WARN] 授权信息刷新失败，返回null
2025-07-31 15:25:17 [DEBUG] 控件权限状态刷新完成，已检查 114 个控件
2025-07-31 15:25:17 [DEBUG] 控件标题刷新完成
2025-07-31 15:25:17 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-31 15:25:17 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-31 15:25:17 [DEBUG] 🔍 HyRibbon反射获取到 122 个字段
2025-07-31 15:25:17 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:17 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-31 15:25:17 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:17 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-31 15:25:17 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:17 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-31 15:25:17 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:17 [INFO] 动态获取到 120 个Ribbon控件引用
2025-07-31 15:25:17 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:17 [INFO] 开始批量更新控件标题，共 120 个控件
2025-07-31 15:25:17 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-31 15:25:17 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-31 15:25:17 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-31 15:25:17 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-31 15:25:17 [INFO] 批量更新控件标题完成，成功更新 107 个控件
2025-07-31 15:25:17 [INFO] 动态批量更新完成，共更新 120 个控件
2025-07-31 15:25:17 [INFO] 控件标题更正完成
2025-07-31 15:25:17 [INFO] 控件标题刷新完成
2025-07-31 15:25:17 [INFO] 权限管理器初始化完成处理结束
2025-07-31 15:25:17 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-31 15:25:17 [DEBUG] 授权验证初始化完成
2025-07-31 15:25:17 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-31 15:25:17 [INFO] 成功加载配置和授权信息
2025-07-31 15:25:17 [INFO] 开始初始化定时器和设置
2025-07-31 15:25:17 [INFO] 定时器和设置初始化完成
2025-07-31 15:25:17 [INFO] 开始VSTO插件启动流程
2025-07-31 15:25:17 [INFO] TopMostForm窗体加载完成
2025-07-31 15:25:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:25:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9641060
2025-07-31 15:25:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9641060)
2025-07-31 15:25:17 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 9641060
2025-07-31 15:25:17 [INFO] 系统事件监控已启动
2025-07-31 15:25:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:25:18 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-07-31 15:25:18 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-31 15:25:18 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-31 15:25:18 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-31 15:25:18 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 15078464
2025-07-31 15:25:18 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-31 15:25:18 [INFO] VSTO插件启动流程完成
2025-07-31 15:25:19 [INFO] 从Remote成功获取到网络授权信息
2025-07-31 15:25:19 [INFO] 网络授权信息已更新并触发回调
2025-07-31 15:25:19 [INFO] 网络授权信息已从 Network 更新
2025-07-31 15:25:19 [INFO] 授权版本: 1.0
2025-07-31 15:25:19 [INFO] 颁发者: ExtensionsTools
2025-07-31 15:25:19 [INFO] 用户数量: 3
2025-07-31 15:25:19 [INFO] 分组权限数量: 2
2025-07-31 15:25:19 [WARN] 配置文件中未找到用户组信息
2025-07-31 15:25:19 [INFO] 已重新设置用户组: []
2025-07-31 15:25:19 [INFO] 用户组信息已重新设置
2025-07-31 15:25:19 [INFO] 立即刷新权限缓存和UI界面
2025-07-31 15:25:19 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-31 15:25:19 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-31 15:25:19 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-31 15:25:19 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-31 15:25:19 [DEBUG] 本地权限缓存已清空
2025-07-31 15:25:19 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-31 15:25:19 [INFO] 启动网络授权信息获取任务（后台更新）
2025-07-31 15:25:19 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-31 15:25:19 [INFO] 所有权限检查完成
2025-07-31 15:25:19 [DEBUG] 权限重新检查完成
2025-07-31 15:25:19 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:25:19 [INFO] 从Remote成功获取到网络授权信息
2025-07-31 15:25:19 [INFO] 网络授权信息已更新并触发回调
2025-07-31 15:25:19 [WARN] 网络授权回调正在处理中，跳过重复调用 (来源: Network)
2025-07-31 15:25:19 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:25:19 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9641060
2025-07-31 15:25:19 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9641060)
2025-07-31 15:25:19 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:25:19 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-31 15:25:19 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:25:19 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:25:19 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:19 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:25:19 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:25:19 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:19 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:25:20 [DEBUG] 开始重置 208 个命令栏
2025-07-31 15:25:20 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:20 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:25:20 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:20 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:25:20 [INFO] UI界面权限状态已更新
2025-07-31 15:25:20 [DEBUG] 开始刷新所有控件权限状态
2025-07-31 15:25:20 [DEBUG] 控件权限状态刷新完成，已检查 114 个控件
2025-07-31 15:25:20 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-31 15:25:20 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-31 15:25:20 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-31 15:25:20 [INFO] 开始刷新Ribbon控件标题
2025-07-31 15:25:20 [DEBUG] 权限缓存已清空，清除了 114 个缓存项
2025-07-31 15:25:20 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-31 15:25:20 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-31 15:25:20 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-31 15:25:20 [DEBUG] 🔍 HyRibbon反射获取到 122 个字段
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-31 15:25:20 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-31 15:25:20 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-31 15:25:20 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:20 [INFO] 动态获取到 120 个Ribbon控件引用
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:20 [INFO] 开始批量更新控件标题，共 120 个控件
2025-07-31 15:25:20 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:25:20 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:20 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:25:20 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:20 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:25:20 [DEBUG] 重置命令栏: cell
2025-07-31 15:25:20 [DEBUG] 重置命令栏: column
2025-07-31 15:25:20 [DEBUG] 重置命令栏: row
2025-07-31 15:25:20 [DEBUG] 重置命令栏: cell
2025-07-31 15:25:20 [DEBUG] 重置命令栏: column
2025-07-31 15:25:20 [DEBUG] 重置命令栏: row
2025-07-31 15:25:20 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-31 15:25:20 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-31 15:25:20 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-31 15:25:20 [DEBUG] 重置命令栏: row
2025-07-31 15:25:20 [INFO] 批量更新控件标题完成，成功更新 107 个控件
2025-07-31 15:25:20 [INFO] 动态批量更新完成，共更新 120 个控件
2025-07-31 15:25:20 [INFO] 控件标题更正完成
2025-07-31 15:25:20 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-31 15:25:20 [INFO] Ribbon控件标题刷新完成
2025-07-31 15:25:20 [INFO] 控件标题刷新完成
2025-07-31 15:25:20 [DEBUG] Ribbon控件标题已刷新
2025-07-31 15:25:20 [INFO] 开始刷新控件标题
2025-07-31 15:25:20 [DEBUG] 开始刷新所有控件权限状态
2025-07-31 15:25:20 [DEBUG] 控件权限状态刷新完成，已检查 114 个控件
2025-07-31 15:25:20 [DEBUG] 控件标题刷新完成
2025-07-31 15:25:20 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-31 15:25:20 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-31 15:25:20 [DEBUG] 🔍 HyRibbon反射获取到 122 个字段
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:20 [DEBUG] 重置命令栏: column
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-31 15:25:20 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-31 15:25:20 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-31 15:25:20 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:20 [INFO] 动态获取到 120 个Ribbon控件引用
2025-07-31 15:25:20 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:20 [INFO] 开始批量更新控件标题，共 120 个控件
2025-07-31 15:25:20 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-31 15:25:20 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-31 15:25:20 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-31 15:25:20 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-31 15:25:20 [INFO] 批量更新控件标题完成，成功更新 107 个控件
2025-07-31 15:25:20 [INFO] 动态批量更新完成，共更新 120 个控件
2025-07-31 15:25:20 [INFO] 控件标题更正完成
2025-07-31 15:25:20 [INFO] 控件标题刷新完成
2025-07-31 15:25:20 [DEBUG] Ribbon控件标题已立即刷新
2025-07-31 15:25:21 [INFO] 开始刷新授权状态
2025-07-31 15:25:21 [DEBUG] 开始初始化授权验证
2025-07-31 15:25:21 [DEBUG] 使用新的权限管理器进行初始化
2025-07-31 15:25:21 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-31 15:25:21 [INFO] 开始初始化UI权限管理
2025-07-31 15:25:21 [DEBUG] [实例ID: b8b4becf] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-31 15:25:21 [DEBUG] 🔍 [实例ID: b8b4becf] 字典引用一致性检查:
2025-07-31 15:25:21 [DEBUG] 🔍   标题映射一致性: True
2025-07-31 15:25:21 [DEBUG] 🔍   权限映射一致性: True
2025-07-31 15:25:21 [DEBUG] 🔍   信息映射一致性: True
2025-07-31 15:25:21 [DEBUG] 🔍   特殊控件一致性: True
2025-07-31 15:25:21 [DEBUG] 控件权限管理器初始化完成 [实例ID: b8b4becf]
2025-07-31 15:25:21 [DEBUG] 开始注册控件权限映射
2025-07-31 15:25:21 [DEBUG] 批量注册控件权限映射完成，成功: 114/114
2025-07-31 15:25:21 [DEBUG] HyExcel控件权限映射注册完成，共注册 114 个控件
2025-07-31 15:25:21 [INFO] 开始初始化权限验证
2025-07-31 15:25:21 [DEBUG] 设置默认UI可见性为false
2025-07-31 15:25:21 [DEBUG] 开始检查所有需要的权限
2025-07-31 15:25:21 [INFO] 所有权限检查完成
2025-07-31 15:25:21 [DEBUG] 应用权限状态到UI控件
2025-07-31 15:25:21 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:25:21 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:21 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:25:21 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:21 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:25:21 [DEBUG] 启动后台权限刷新任务
2025-07-31 15:25:21 [DEBUG] 启动延迟权限刷新任务
2025-07-31 15:25:21 [INFO] 权限验证初始化完成
2025-07-31 15:25:21 [INFO] UI权限管理初始化完成
2025-07-31 15:25:21 [INFO] 收到权限管理器初始化完成通知
2025-07-31 15:25:21 [INFO] 开始刷新控件标题
2025-07-31 15:25:21 [DEBUG] 开始刷新所有控件权限状态
2025-07-31 15:25:21 [DEBUG] 控件权限状态刷新完成，已检查 114 个控件
2025-07-31 15:25:21 [DEBUG] 控件标题刷新完成
2025-07-31 15:25:21 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-31 15:25:21 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-31 15:25:21 [DEBUG] 🔍 HyRibbon反射获取到 122 个字段
2025-07-31 15:25:21 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:21 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-31 15:25:21 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:21 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-31 15:25:21 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:21 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-31 15:25:21 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-31 15:25:21 [INFO] 动态获取到 120 个Ribbon控件引用
2025-07-31 15:25:21 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:25:21 [INFO] 开始批量更新控件标题，共 120 个控件
2025-07-31 15:25:21 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:25:21 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:21 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:25:21 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:21 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:25:21 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-31 15:25:21 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-31 15:25:21 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-31 15:25:21 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-31 15:25:21 [INFO] 批量更新控件标题完成，成功更新 107 个控件
2025-07-31 15:25:21 [INFO] 动态批量更新完成，共更新 120 个控件
2025-07-31 15:25:21 [INFO] 控件标题更正完成
2025-07-31 15:25:21 [INFO] 控件标题刷新完成
2025-07-31 15:25:21 [INFO] 权限管理器初始化完成处理结束
2025-07-31 15:25:21 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-31 15:25:21 [DEBUG] 授权验证初始化完成
2025-07-31 15:25:21 [INFO] 授权状态刷新完成
2025-07-31 15:25:21 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-31 15:25:22 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:25:22 [DEBUG] 授权控制器已初始化
2025-07-31 15:25:22 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:25:23 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:25:23 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:25:23 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:23 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:25:23 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:25:23 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:25:23 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:25:23 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:25:24 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:25:24 [DEBUG] 授权控制器已初始化
2025-07-31 15:25:24 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:25:24 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:25:25 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:25:25 [DEBUG] 授权控制器已初始化
2025-07-31 15:25:25 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:25:26 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:25:26 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:25:26 [DEBUG] 授权控制器已初始化
2025-07-31 15:25:26 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:25:27 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:25:27 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:25:27 [DEBUG] 授权控制器已初始化
2025-07-31 15:25:27 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:25:28 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:25:28 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:25:28 [DEBUG] 授权控制器已初始化
2025-07-31 15:25:28 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:25:29 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:25:29 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:25:29 [DEBUG] 授权控制器已初始化
2025-07-31 15:25:29 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:25:30 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:25:30 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:25:30 [DEBUG] 授权控制器已初始化
2025-07-31 15:25:30 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:25:31 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:25:32 [DEBUG] 已重置工作表标签菜单
2025-07-31 15:25:32 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:28:37 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:28:37 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9641060
2025-07-31 15:28:37 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9641060)
2025-07-31 15:28:37 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:28:37 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-31 15:28:38 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:28:38 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9641060
2025-07-31 15:28:38 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9641060)
2025-07-31 15:28:38 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:28:38 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-31 15:28:48 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:28:48 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:28:48 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:28:48 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:28:48 [INFO] OpenForm: 保持窗体现有标题 '订单KML点图生成工具'
2025-07-31 15:28:48 [INFO] OpenForm: 准备打开窗体 '订单KML点图生成工具'，位置: Center，单实例: True
2025-07-31 15:28:48 [INFO] 开始显示窗体 '订单KML点图生成工具'，位置模式: Center
2025-07-31 15:28:48 [DEBUG] 配置设置绑定完成
2025-07-31 15:28:48 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:28:48 [INFO] [ET.Controls.ETLogDisplayControl] === 订单KML点图生成工具 ===
2025-07-31 15:28:48 [INFO] [ET.Controls.ETLogDisplayControl] 版本: v1.0
2025-07-31 15:28:48 [INFO] [ET.Controls.ETLogDisplayControl] 启动时间: 2025-07-31 15:28:48
2025-07-31 15:28:48 [INFO] [ET.Controls.ETLogDisplayControl] 
2025-07-31 15:28:48 [INFO] 订单KML点图生成工具已启动
2025-07-31 15:28:48 [INFO] [ET.Controls.ETLogDisplayControl] 欢迎使用订单KML点图生成工具！
2025-07-31 15:28:48 [INFO] [ET.Controls.ETLogDisplayControl] 请选择包含订单数据的Excel文件，然后设置KML输出路径。
2025-07-31 15:28:48 [INFO] 窗体 '订单KML点图生成工具' 以TopMostForm为父窗体显示
2025-07-31 15:28:48 [INFO] 窗体 '订单KML点图生成工具' 显示完成，句柄: 5246336
2025-07-31 15:28:48 [INFO] OpenForm: 窗体 '订单KML点图生成工具' 打开成功
2025-07-31 15:28:49 [INFO] OrderKmlGeneratorForm资源清理完成
2025-07-31 15:28:49 [INFO] 订单KML点图生成工具已关闭
2025-07-31 15:28:49 [INFO] OrderKmlGeneratorForm资源清理完成
2025-07-31 15:28:49 [INFO] OrderKmlGeneratorHelper正在释放资源
2025-07-31 15:28:49 [INFO] OrderDataExtractor正在释放资源
2025-07-31 15:28:49 [INFO] OrderDataExtractor资源释放完成
2025-07-31 15:28:49 [INFO] OrderKmlGeneratorHelper资源释放完成
2025-07-31 15:28:49 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-31 15:28:53 [INFO] OpenForm: 窗体标题已设置为类名 '格式化经纬度'
2025-07-31 15:28:53 [INFO] OpenForm: 准备打开窗体 '格式化经纬度'，位置: Center，单实例: True
2025-07-31 15:28:53 [INFO] 开始显示窗体 '格式化经纬度'，位置模式: Center
2025-07-31 15:28:53 [INFO] 窗体 '格式化经纬度' 以TopMostForm为父窗体显示
2025-07-31 15:28:53 [INFO] 窗体 '格式化经纬度' 显示完成，句柄: 1976412
2025-07-31 15:28:53 [INFO] OpenForm: 窗体 '格式化经纬度' 打开成功
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:28:55 [INFO] OpenForm: 保持窗体现有标题 'GPS生成KML图层'
2025-07-31 15:28:55 [INFO] OpenForm: 准备打开窗体 'GPS生成KML图层'，位置: Center，单实例: True
2025-07-31 15:28:55 [INFO] 开始显示窗体 'GPS生成KML图层'，位置模式: Center
2025-07-31 15:28:55 [DEBUG] [ET.Controls.ETRangeSelectControl] 文本框获得焦点，上次地址：
2025-07-31 15:28:55 [INFO] 窗体 'GPS生成KML图层' 以TopMostForm为父窗体显示
2025-07-31 15:28:55 [INFO] 窗体 'GPS生成KML图层' 显示完成，句柄: 2297780
2025-07-31 15:28:55 [INFO] OpenForm: 窗体 'GPS生成KML图层' 打开成功
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:28:58 [INFO] OpenForm: 保持窗体现有标题 'GPS生成KML图层'
2025-07-31 15:28:58 [INFO] OpenForm: 准备打开窗体 'GPS生成KML图层'，位置: Center，单实例: True
2025-07-31 15:28:58 [INFO] 开始显示窗体 'GPS生成KML图层'，位置模式: Center
2025-07-31 15:28:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 文本框获得焦点，上次地址：
2025-07-31 15:28:58 [INFO] 窗体 'GPS生成KML图层' 以TopMostForm为父窗体显示
2025-07-31 15:28:58 [INFO] 窗体 'GPS生成KML图层' 显示完成，句柄: 1773448
2025-07-31 15:28:58 [INFO] OpenForm: 窗体 'GPS生成KML图层' 打开成功
2025-07-31 15:29:03 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:03 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:03 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:03 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:29:03 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-07-31 15:29:03 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:03 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:03 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:29:03 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:03 [INFO] [ET.Controls.ETLogDisplayControl] 站点数据转换器已就绪
2025-07-31 15:29:03 [INFO] [ET.Controls.ETLogDisplayControl] 功能说明：
2025-07-31 15:29:03 [INFO] [ET.Controls.ETLogDisplayControl] • 创建空白工作簿：自动创建包含"数据来源"和"数据输出"表的标准工作簿
2025-07-31 15:29:03 [INFO] [ET.Controls.ETLogDisplayControl] • 执行站点转换：将逻辑站点数据转换为物理站点汇总数据
2025-07-31 15:29:03 [INFO] [ET.Controls.ETLogDisplayControl] • 支持智能站点分组、动态频段配置和高性能GPS算法
2025-07-31 15:29:03 [INFO] [HyExcelVsto.Module.WX.StationConverter.StationConverterForm, Text: 站点系统数量统计] StationConverterForm初始化完成
2025-07-31 15:29:03 [INFO] OpenForm: 保持窗体现有标题 '站点系统数量统计'
2025-07-31 15:29:03 [INFO] OpenForm: 准备打开窗体 '站点系统数量统计'，位置: Center，单实例: False
2025-07-31 15:29:03 [INFO] 开始显示窗体 '站点系统数量统计'，位置模式: Center
2025-07-31 15:29:03 [INFO] 窗体 '站点系统数量统计' 以TopMostForm为父窗体显示
2025-07-31 15:29:03 [INFO] 窗体 '站点系统数量统计' 显示完成，句柄: 2169618
2025-07-31 15:29:03 [INFO] OpenForm: 窗体 '站点系统数量统计' 打开成功
2025-07-31 15:29:04 [INFO] [HyExcelVsto.Module.WX.StationConverter.StationConverterForm, Text: 站点系统数量统计] StationConverterForm资源已释放
2025-07-31 15:29:04 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-31 15:29:05 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:05 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:05 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:05 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:29:05 [INFO] OpenForm: 保持窗体现有标题 '订单KML点图生成工具'
2025-07-31 15:29:05 [INFO] OpenForm: 准备打开窗体 '订单KML点图生成工具'，位置: Center，单实例: True
2025-07-31 15:29:05 [INFO] 开始显示窗体 '订单KML点图生成工具'，位置模式: Center
2025-07-31 15:29:05 [DEBUG] 配置设置绑定完成
2025-07-31 15:29:05 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:29:05 [INFO] [ET.Controls.ETLogDisplayControl] === 订单KML点图生成工具 ===
2025-07-31 15:29:05 [INFO] [ET.Controls.ETLogDisplayControl] 版本: v1.0
2025-07-31 15:29:05 [INFO] [ET.Controls.ETLogDisplayControl] 启动时间: 2025-07-31 15:29:05
2025-07-31 15:29:05 [INFO] [ET.Controls.ETLogDisplayControl] 
2025-07-31 15:29:05 [INFO] 订单KML点图生成工具已启动
2025-07-31 15:29:05 [INFO] [ET.Controls.ETLogDisplayControl] 欢迎使用订单KML点图生成工具！
2025-07-31 15:29:05 [INFO] [ET.Controls.ETLogDisplayControl] 请选择包含订单数据的Excel文件，然后设置KML输出路径。
2025-07-31 15:29:05 [INFO] 窗体 '订单KML点图生成工具' 以TopMostForm为父窗体显示
2025-07-31 15:29:05 [INFO] 窗体 '订单KML点图生成工具' 显示完成，句柄: 5639552
2025-07-31 15:29:05 [INFO] OpenForm: 窗体 '订单KML点图生成工具' 打开成功
2025-07-31 15:29:06 [INFO] OrderKmlGeneratorForm资源清理完成
2025-07-31 15:29:06 [INFO] 订单KML点图生成工具已关闭
2025-07-31 15:29:06 [INFO] OrderKmlGeneratorForm资源清理完成
2025-07-31 15:29:06 [INFO] OrderKmlGeneratorHelper正在释放资源
2025-07-31 15:29:06 [INFO] OrderDataExtractor正在释放资源
2025-07-31 15:29:06 [INFO] OrderDataExtractor资源释放完成
2025-07-31 15:29:06 [INFO] OrderKmlGeneratorHelper资源释放完成
2025-07-31 15:29:06 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:08 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置Excel应用程序提供者：VSTOExcelApplicationProvider
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置Excel应用程序提供者：VSTOExcelApplicationProvider
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置Excel应用程序提供者：VSTOExcelApplicationProvider
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:08 [INFO] [ET.Controls.ETLogDisplayControl] 方向角/下倾角提取器已就绪
2025-07-31 15:29:08 [INFO] [ET.Controls.ETLogDisplayControl] 本功能：用于提取方向角和下倾角，并填写到指定列
注意：只有1小区的数据无法识别，该功能目前有Bug，不是很准确

使用方法：
1. 选择包含原始数据的来源范围
2. 选择方向角输出列（可选）
3. 选择下倾角输出列（可选）
4. 点击提取按钮执行操作

提取规则：
- 方向角：识别格式如 120/240/360 的数字组合
- 下倾角：识别范围在-5到20度之间的角度值
- 自动跳过筛选行和标题行
2025-07-31 15:29:08 [INFO] [HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm, Text: 方向角/下倾角提取器] 方向角提取器初始化完成
2025-07-31 15:29:08 [INFO] [HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm, Text: 方向角/下倾角提取器] AngleExtractorForm初始化完成
2025-07-31 15:29:08 [INFO] OpenForm: 保持窗体现有标题 '方向角/下倾角提取器'
2025-07-31 15:29:08 [INFO] OpenForm: 准备打开窗体 '方向角/下倾角提取器'，位置: Center，单实例: False
2025-07-31 15:29:08 [INFO] 开始显示窗体 '方向角/下倾角提取器'，位置模式: Center
2025-07-31 15:29:08 [DEBUG] [ET.Controls.ETRangeSelectControl] 文本框获得焦点，上次地址：
2025-07-31 15:29:08 [INFO] 窗体 '方向角/下倾角提取器' 以TopMostForm为父窗体显示
2025-07-31 15:29:08 [INFO] 窗体 '方向角/下倾角提取器' 显示完成，句柄: 3543596
2025-07-31 15:29:08 [INFO] OpenForm: 窗体 '方向角/下倾角提取器' 打开成功
2025-07-31 15:29:09 [INFO] [HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm, Text: 方向角/下倾角提取器] AngleExtractorForm资源已释放
2025-07-31 15:29:09 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:10 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置Excel应用程序提供者：VSTOExcelApplicationProvider
2025-07-31 15:29:10 [INFO] 创建配置目录: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.stationdata
2025-07-31 15:29:10 [INFO] 找到 0 个配置文件
2025-07-31 15:29:10 [INFO] [HyExcelVsto.Module.WX.StationDataProcessor.StationDataProcessorForm, Text: 基站台账数据转换处理] 找到 0 个配置文件
2025-07-31 15:29:10 [INFO] [HyExcelVsto.Module.WX.StationDataProcessor.StationDataProcessorForm, Text: 基站台账数据转换处理] 当前选择的配置文件：StationDataProcessor_4G.config
2025-07-31 15:29:10 [INFO] [ET.Controls.ETLogDisplayControl] 已选择工作表：Sheet1，范围：$A$1
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'Sheet1'!$A$1
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'Sheet1'!$A$1，触发事件：True
2025-07-31 15:29:10 [INFO] [HyExcelVsto.Module.WX.StationDataProcessor.StationDataProcessorForm, Text: 基站台账数据转换处理] 已设置默认数据区域：工作表 'Sheet1'，范围 '$A$1'
2025-07-31 15:29:10 [INFO] [HyExcelVsto.Module.WX.StationDataProcessor.StationDataProcessorForm, Text: 基站台账数据转换处理] StationDataProcessorForm初始化完成
2025-07-31 15:29:10 [INFO] OpenForm: 保持窗体现有标题 '基站台账数据转换处理'
2025-07-31 15:29:10 [INFO] OpenForm: 准备打开窗体 '基站台账数据转换处理'，位置: Center，单实例: False
2025-07-31 15:29:10 [INFO] 开始显示窗体 '基站台账数据转换处理'，位置模式: Center
2025-07-31 15:29:10 [DEBUG] [ET.Controls.ETRangeSelectControl] 文本框获得焦点，上次地址：'Sheet1'!$A$1
2025-07-31 15:29:10 [INFO] 窗体 '基站台账数据转换处理' 以TopMostForm为父窗体显示
2025-07-31 15:29:10 [INFO] 窗体 '基站台账数据转换处理' 显示完成，句柄: 4397382
2025-07-31 15:29:10 [INFO] OpenForm: 窗体 '基站台账数据转换处理' 打开成功
2025-07-31 15:29:13 [INFO] [HyExcelVsto.Module.WX.StationDataProcessor.StationDataProcessorForm, Text: 基站台账数据转换处理] StationDataProcessorForm资源已释放
2025-07-31 15:29:13 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-31 15:29:15 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:15 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:15 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:15 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:29:15 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-07-31 15:29:15 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:29:15 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:29:15 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:29:15 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:29:15 [INFO] [ET.Controls.ETLogDisplayControl] 铁塔内部台账梳理功能已就绪
2025-07-31 15:29:15 [INFO] [ET.Controls.ETLogDisplayControl] 铁塔内部台账梳理功能：

1. 铁塔内部台账提取电信部分：
   - 验证工作表类型（必须是'三家运营商会审汇总表'）
   - 复制工作表到新工作簿
   - 删除首行并设置筛选
   - 根据审核人员筛选电信相关数据
   - 按会审日期降序、序号升序排序
   - 设置冻结窗格和筛选行

2. 汇总新铁塔台账到铁塔会审台账：
   - 选择来源表和汇总表
   - 根据会审日期去重汇总数据
   - 复制公式并转换为数值
   - 自动插入新数据到汇总表

注意：本功能针对性强，代码是硬编码，如需改动需直接修改代码
2025-07-31 15:29:15 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] 铁塔台账处理器初始化完成
2025-07-31 15:29:15 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] TowerAccountProcessorForm初始化完成
2025-07-31 15:29:15 [INFO] OpenForm: 保持窗体现有标题 '铁塔内部台账转换工具'
2025-07-31 15:29:15 [INFO] OpenForm: 准备打开窗体 '铁塔内部台账转换工具'，位置: Center，单实例: False
2025-07-31 15:29:15 [INFO] 开始显示窗体 '铁塔内部台账转换工具'，位置模式: Center
2025-07-31 15:29:15 [INFO] 窗体 '铁塔内部台账转换工具' 以TopMostForm为父窗体显示
2025-07-31 15:29:15 [INFO] 窗体 '铁塔内部台账转换工具' 显示完成，句柄: ********
2025-07-31 15:29:15 [INFO] OpenForm: 窗体 '铁塔内部台账转换工具' 打开成功
2025-07-31 15:29:17 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] TowerAccountProcessorForm资源已释放
2025-07-31 15:29:17 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-31 15:29:20 [INFO] OpenForm: 保持窗体现有标题 'KML转换器 - 添加备注信息'
2025-07-31 15:29:20 [INFO] OpenForm: 准备打开窗体 'KML转换器 - 添加备注信息'，位置: Center，单实例: True
2025-07-31 15:29:20 [INFO] 开始显示窗体 'KML转换器 - 添加备注信息'，位置模式: Center
2025-07-31 15:29:20 [INFO] 窗体 'KML转换器 - 添加备注信息' 以TopMostForm为父窗体显示
2025-07-31 15:29:20 [INFO] 窗体 'KML转换器 - 添加备注信息' 显示完成，句柄: ********
2025-07-31 15:29:20 [INFO] OpenForm: 窗体 'KML转换器 - 添加备注信息' 打开成功
2025-07-31 15:34:01 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:34:01 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 9641060, 新父窗口: 2559652
2025-07-31 15:34:01 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2559652)
2025-07-31 15:34:01 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:34:01 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-07-31 15:34:01 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:34:01 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 2559652)
2025-07-31 15:34:01 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:34:01 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-31 15:34:02 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:34:02 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2559652
2025-07-31 15:34:02 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2559652)
2025-07-31 15:34:02 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:34:02 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-31 15:34:02 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:34:02 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2559652
2025-07-31 15:34:02 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2559652)
2025-07-31 15:34:02 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:34:18 [INFO] [ET.ETForm] 已创建默认配置文件：D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\收藏文件.config
2025-07-31 15:34:18 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-07-31 15:34:21 [INFO] [ET.ETForm] 用户点击刷新菜单：收藏文件.config
2025-07-31 15:34:21 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-07-31 15:34:23 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-07-31 15:34:52 [INFO] App_WorkbookOpen: 工作簿 '☆51交付管理.xlsx' 打开事件触发
2025-07-31 15:34:52 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:34:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 2559652, 新父窗口: 88477062
2025-07-31 15:34:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:34:52 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:34:52 [INFO] App_WorkbookOpen: 工作簿 '☆51交付管理.xlsx' 打开处理完成
2025-07-31 15:34:52 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:34:52 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 88477062)
2025-07-31 15:34:52 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:34:52 [INFO] App_WorkbookActivate: 工作簿 '☆51交付管理.xlsx' 激活处理完成
2025-07-31 15:34:52 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:34:52 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 88477062)
2025-07-31 15:34:52 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:34:52 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-31 15:34:53 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:34:53 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:34:53 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 88477062
2025-07-31 15:34:53 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:34:53 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:34:53 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-31 15:34:53 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 88477062)
2025-07-31 15:34:53 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:34:53 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:34:53 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 88477062
2025-07-31 15:34:53 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:34:53 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:34:53 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-31 15:44:06 [INFO] Excel窗口句柄监控器初始化完成
2025-07-31 15:44:07 [INFO] 配置文件实例已在加载时初始化
2025-07-31 15:44:07 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-31 15:44:07 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-31 15:44:07 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-31 15:44:07 [INFO] 成功初始化Excel应用程序实例
2025-07-31 15:44:07 [INFO] 自动备份路径未配置
2025-07-31 15:44:07 [DEBUG] 开始初始化授权控制器
2025-07-31 15:44:08 [DEBUG] 授权系统初始化完成，耗时: 457ms
2025-07-31 15:44:08 [DEBUG] 开始初始化授权验证
2025-07-31 15:44:08 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-31 15:44:08 [DEBUG] 权限管理器初始化成功
2025-07-31 15:44:08 [DEBUG] 使用新的权限管理器进行初始化
2025-07-31 15:44:08 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-31 15:44:08 [INFO] 开始初始化UI权限管理
2025-07-31 15:44:08 [DEBUG] [实例ID: 609ff2a1] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-31 15:44:08 [DEBUG] 🔍 [实例ID: 609ff2a1] 字典引用一致性检查:
2025-07-31 15:44:08 [DEBUG] 🔍   标题映射一致性: True
2025-07-31 15:44:08 [DEBUG] 🔍   权限映射一致性: True
2025-07-31 15:44:08 [DEBUG] 🔍   信息映射一致性: True
2025-07-31 15:44:08 [DEBUG] 🔍   特殊控件一致性: True
2025-07-31 15:44:08 [DEBUG] 控件权限管理器初始化完成 [实例ID: 609ff2a1]
2025-07-31 15:44:08 [DEBUG] 开始注册控件权限映射
2025-07-31 15:44:08 [INFO] 开始初始化全局控件映射
2025-07-31 15:44:08 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-31 15:44:08 [DEBUG] 开始生成控件标题映射
2025-07-31 15:44:08 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-31 15:44:08 [DEBUG] 通过反射获取到 123 个字段
2025-07-31 15:44:08 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-31 15:44:08 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-31 15:44:08 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-31 15:44:08 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-07-31 15:44:08 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-31 15:44:08 [INFO] 控件标题映射生成完成，共生成 108 项映射
2025-07-31 15:44:08 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-31 15:44:08 [DEBUG] 全局控件标题映射生成完成，共生成 108 项
2025-07-31 15:44:08 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-31 15:44:08 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-31 15:44:08 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-31 15:44:08 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-31 15:44:08 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-31 15:44:08 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-31 15:44:08 [INFO] === znAbout控件标题映射诊断 ===
2025-07-31 15:44:08 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-31 15:44:08 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-31 15:44:08 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-31 15:44:08 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-31 15:44:08 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-31 15:44:08 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-07-31 15:44:08 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-07-31 15:44:08 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-07-31 15:44:08 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-31 15:44:08 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-31 15:44:08 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-31 15:44:08 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-31 15:44:08 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-31 15:44:08 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-07-31 15:44:08 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-31 15:44:08 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-31 15:44:08 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-31 15:44:08 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-31 15:44:08 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-31 15:44:08 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-31 15:44:08 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-31 15:44:08 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-31 15:44:08 [DEBUG] 控件映射: button3 -> '关于'
2025-07-31 15:44:08 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-31 15:44:08 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-31 15:44:08 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-31 15:44:08 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-31 15:44:08 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-31 15:44:08 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-31 15:44:08 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-31 15:44:08 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-31 15:44:08 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-31 15:44:08 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-31 15:44:08 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-31 15:44:08 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-07-31 15:44:08 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-31 15:44:08 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-31 15:44:08 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-31 15:44:08 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-31 15:44:08 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-31 15:44:08 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-31 15:44:08 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-31 15:44:08 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-31 15:44:08 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-07-31 15:44:08 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-31 15:44:08 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-31 15:44:08 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-31 15:44:08 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-07-31 15:44:08 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-31 15:44:08 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-31 15:44:08 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-31 15:44:08 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-31 15:44:08 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-31 15:44:08 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-31 15:44:08 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-31 15:44:08 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-31 15:44:08 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-31 15:44:08 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-31 15:44:08 [DEBUG] 控件映射: group1 -> '关于'
2025-07-31 15:44:08 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-31 15:44:08 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-31 15:44:08 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-31 15:44:08 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-31 15:44:08 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-31 15:44:08 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-31 15:44:08 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-31 15:44:08 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-31 15:44:08 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-31 15:44:08 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-07-31 15:44:08 [DEBUG] 控件映射: menu2 -> '其它'
2025-07-31 15:44:08 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-31 15:44:08 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-31 15:44:08 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-31 15:44:08 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-31 15:44:08 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-31 15:44:08 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-31 15:44:08 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-31 15:44:08 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-31 15:44:08 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-31 15:44:08 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-31 15:44:08 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-31 15:44:08 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-31 15:44:08 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-31 15:44:08 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-31 15:44:08 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-31 15:44:08 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-31 15:44:08 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-31 15:44:08 [DEBUG] 开始生成控件权限映射
2025-07-31 15:44:08 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-31 15:44:08 [DEBUG] 通过反射获取到 123 个字段
2025-07-31 15:44:08 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-31 15:44:08 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-31 15:44:08 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-31 15:44:08 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-31 15:44:08 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-31 15:44:08 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-31 15:44:08 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-07-31 15:44:08 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-31 15:44:08 [INFO] 控件权限映射生成完成，共生成 115 项映射
2025-07-31 15:44:08 [DEBUG] 全局控件权限映射生成完成，共生成 115 项
2025-07-31 15:44:08 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-31 15:44:08 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-31 15:44:08 [INFO] 全局控件映射初始化完成 - 标题映射: 108 项, 权限映射: 115 项
2025-07-31 15:44:08 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-07-31 15:44:08 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-07-31 15:44:08 [INFO] 开始初始化权限验证
2025-07-31 15:44:08 [DEBUG] 设置默认UI可见性为false
2025-07-31 15:44:08 [DEBUG] 开始检查所有需要的权限
2025-07-31 15:44:08 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-31 15:44:09 [INFO] 启动网络授权信息获取任务
2025-07-31 15:44:09 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-31 15:44:09 [INFO] 所有权限检查完成
2025-07-31 15:44:09 [DEBUG] 应用权限状态到UI控件
2025-07-31 15:44:09 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:44:09 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:09 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:44:09 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:09 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:44:09 [DEBUG] 启动后台权限刷新任务
2025-07-31 15:44:09 [DEBUG] 启动延迟权限刷新任务
2025-07-31 15:44:09 [INFO] 权限验证初始化完成
2025-07-31 15:44:09 [INFO] UI权限管理初始化完成
2025-07-31 15:44:09 [INFO] 收到权限管理器初始化完成通知
2025-07-31 15:44:09 [INFO] 开始刷新控件标题
2025-07-31 15:44:09 [DEBUG] 开始刷新所有控件权限状态
2025-07-31 15:44:09 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-07-31 15:44:09 [DEBUG] 控件标题刷新完成
2025-07-31 15:44:09 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-31 15:44:09 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-31 15:44:09 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-07-31 15:44:09 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:09 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-31 15:44:09 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:09 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-31 15:44:09 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:09 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-31 15:44:09 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:09 [INFO] 动态获取到 121 个Ribbon控件引用
2025-07-31 15:44:09 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:09 [INFO] 开始批量更新控件标题，共 121 个控件
2025-07-31 15:44:09 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-31 15:44:09 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-31 15:44:09 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-31 15:44:09 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-31 15:44:09 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-07-31 15:44:09 [INFO] 动态批量更新完成，共更新 121 个控件
2025-07-31 15:44:09 [INFO] 控件标题更正完成
2025-07-31 15:44:09 [INFO] 控件标题刷新完成
2025-07-31 15:44:09 [INFO] 权限管理器初始化完成处理结束
2025-07-31 15:44:09 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-31 15:44:09 [DEBUG] 授权验证初始化完成
2025-07-31 15:44:09 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-31 15:44:09 [INFO] 成功加载配置和授权信息
2025-07-31 15:44:09 [INFO] 开始初始化定时器和设置
2025-07-31 15:44:09 [INFO] 定时器和设置初始化完成
2025-07-31 15:44:09 [INFO] 开始VSTO插件启动流程
2025-07-31 15:44:09 [INFO] TopMostForm窗体加载完成
2025-07-31 15:44:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3936808
2025-07-31 15:44:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3936808)
2025-07-31 15:44:09 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 3936808
2025-07-31 15:44:09 [INFO] 系统事件监控已启动
2025-07-31 15:44:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:09 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-07-31 15:44:09 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-31 15:44:09 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-31 15:44:09 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-31 15:44:09 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 69403198
2025-07-31 15:44:09 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-31 15:44:09 [INFO] VSTO插件启动流程完成
2025-07-31 15:44:10 [INFO] 从Remote成功获取到网络授权信息
2025-07-31 15:44:10 [INFO] 网络授权信息已更新并触发回调
2025-07-31 15:44:10 [INFO] 网络授权信息已从 Network 更新
2025-07-31 15:44:10 [INFO] 授权版本: 1.0
2025-07-31 15:44:10 [INFO] 颁发者: ExtensionsTools
2025-07-31 15:44:10 [INFO] 用户数量: 3
2025-07-31 15:44:10 [INFO] 分组权限数量: 2
2025-07-31 15:44:10 [WARN] 配置文件中未找到用户组信息
2025-07-31 15:44:10 [INFO] 已重新设置用户组: []
2025-07-31 15:44:10 [INFO] 用户组信息已重新设置
2025-07-31 15:44:10 [INFO] 立即刷新权限缓存和UI界面
2025-07-31 15:44:10 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-31 15:44:10 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-31 15:44:10 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-31 15:44:10 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-31 15:44:10 [DEBUG] 本地权限缓存已清空
2025-07-31 15:44:10 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-31 15:44:10 [INFO] 所有权限检查完成
2025-07-31 15:44:10 [DEBUG] 权限重新检查完成
2025-07-31 15:44:10 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:44:10 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:10 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:44:10 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:10 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:44:10 [INFO] UI界面权限状态已更新
2025-07-31 15:44:10 [DEBUG] 开始刷新所有控件权限状态
2025-07-31 15:44:10 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-07-31 15:44:10 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-31 15:44:10 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-31 15:44:10 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-31 15:44:10 [INFO] 开始刷新Ribbon控件标题
2025-07-31 15:44:10 [DEBUG] 权限缓存已清空，清除了 115 个缓存项
2025-07-31 15:44:10 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-31 15:44:10 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-31 15:44:10 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-31 15:44:10 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-31 15:44:10 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-31 15:44:10 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-31 15:44:10 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:10 [INFO] 动态获取到 121 个Ribbon控件引用
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:10 [INFO] 开始批量更新控件标题，共 121 个控件
2025-07-31 15:44:10 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-31 15:44:10 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-31 15:44:10 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-31 15:44:10 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-07-31 15:44:10 [INFO] 动态批量更新完成，共更新 121 个控件
2025-07-31 15:44:10 [INFO] 控件标题更正完成
2025-07-31 15:44:10 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-31 15:44:10 [INFO] Ribbon控件标题刷新完成
2025-07-31 15:44:10 [INFO] 控件标题刷新完成
2025-07-31 15:44:10 [DEBUG] Ribbon控件标题已刷新
2025-07-31 15:44:10 [INFO] 开始刷新控件标题
2025-07-31 15:44:10 [DEBUG] 开始刷新所有控件权限状态
2025-07-31 15:44:10 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-07-31 15:44:10 [DEBUG] 控件标题刷新完成
2025-07-31 15:44:10 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-31 15:44:10 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-31 15:44:10 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-31 15:44:10 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-31 15:44:10 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-31 15:44:10 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:10 [INFO] 动态获取到 121 个Ribbon控件引用
2025-07-31 15:44:10 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:10 [INFO] 开始批量更新控件标题，共 121 个控件
2025-07-31 15:44:10 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-31 15:44:10 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-31 15:44:10 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-31 15:44:10 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-31 15:44:10 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-07-31 15:44:10 [INFO] 动态批量更新完成，共更新 121 个控件
2025-07-31 15:44:10 [INFO] 控件标题更正完成
2025-07-31 15:44:10 [INFO] 控件标题刷新完成
2025-07-31 15:44:10 [DEBUG] Ribbon控件标题已立即刷新
2025-07-31 15:44:10 [INFO] 开始刷新授权状态
2025-07-31 15:44:10 [DEBUG] 开始初始化授权验证
2025-07-31 15:44:10 [DEBUG] 使用新的权限管理器进行初始化
2025-07-31 15:44:10 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-31 15:44:10 [INFO] 开始初始化UI权限管理
2025-07-31 15:44:10 [DEBUG] [实例ID: 03790f45] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-31 15:44:10 [DEBUG] 🔍 [实例ID: 03790f45] 字典引用一致性检查:
2025-07-31 15:44:10 [DEBUG] 🔍   标题映射一致性: True
2025-07-31 15:44:10 [DEBUG] 🔍   权限映射一致性: True
2025-07-31 15:44:10 [DEBUG] 🔍   信息映射一致性: True
2025-07-31 15:44:10 [DEBUG] 🔍   特殊控件一致性: True
2025-07-31 15:44:10 [DEBUG] 控件权限管理器初始化完成 [实例ID: 03790f45]
2025-07-31 15:44:10 [DEBUG] 开始注册控件权限映射
2025-07-31 15:44:10 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-07-31 15:44:10 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-07-31 15:44:10 [INFO] 开始初始化权限验证
2025-07-31 15:44:10 [DEBUG] 设置默认UI可见性为false
2025-07-31 15:44:11 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:11 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3936808
2025-07-31 15:44:11 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3936808)
2025-07-31 15:44:11 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:11 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-31 15:44:11 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:44:11 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:11 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:44:11 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:11 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:44:11 [DEBUG] 开始重置 208 个命令栏
2025-07-31 15:44:11 [DEBUG] 开始检查所有需要的权限
2025-07-31 15:44:11 [INFO] 所有权限检查完成
2025-07-31 15:44:11 [DEBUG] 应用权限状态到UI控件
2025-07-31 15:44:11 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:44:11 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:11 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:44:11 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:11 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:44:11 [DEBUG] 启动后台权限刷新任务
2025-07-31 15:44:11 [DEBUG] 启动延迟权限刷新任务
2025-07-31 15:44:11 [INFO] 权限验证初始化完成
2025-07-31 15:44:11 [INFO] UI权限管理初始化完成
2025-07-31 15:44:11 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:44:11 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:11 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:44:11 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:11 [INFO] 收到权限管理器初始化完成通知
2025-07-31 15:44:11 [INFO] 开始刷新控件标题
2025-07-31 15:44:11 [DEBUG] 开始刷新所有控件权限状态
2025-07-31 15:44:11 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-07-31 15:44:11 [DEBUG] 控件标题刷新完成
2025-07-31 15:44:11 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-31 15:44:11 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-31 15:44:11 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-07-31 15:44:11 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:11 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:44:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-31 15:44:11 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:44:11 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:11 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:44:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:11 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:11 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:44:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-31 15:44:11 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:44:11 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-31 15:44:11 [INFO] 动态获取到 121 个Ribbon控件引用
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:11 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-31 15:44:11 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:44:11 [INFO] 开始批量更新控件标题，共 121 个控件
2025-07-31 15:44:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-31 15:44:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-31 15:44:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-31 15:44:11 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-31 15:44:11 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-07-31 15:44:11 [INFO] 动态批量更新完成，共更新 121 个控件
2025-07-31 15:44:11 [INFO] 控件标题更正完成
2025-07-31 15:44:11 [INFO] 控件标题刷新完成
2025-07-31 15:44:11 [INFO] 权限管理器初始化完成处理结束
2025-07-31 15:44:11 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-31 15:44:11 [DEBUG] 授权验证初始化完成
2025-07-31 15:44:11 [INFO] 授权状态刷新完成
2025-07-31 15:44:11 [DEBUG] 重置命令栏: cell
2025-07-31 15:44:11 [DEBUG] 重置命令栏: column
2025-07-31 15:44:11 [DEBUG] 重置命令栏: row
2025-07-31 15:44:11 [DEBUG] 重置命令栏: cell
2025-07-31 15:44:11 [DEBUG] 重置命令栏: column
2025-07-31 15:44:11 [DEBUG] 重置命令栏: row
2025-07-31 15:44:12 [DEBUG] 重置命令栏: row
2025-07-31 15:44:12 [DEBUG] 重置命令栏: column
2025-07-31 15:44:12 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-31 15:44:13 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:13 [DEBUG] 授权控制器已初始化
2025-07-31 15:44:13 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:44:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-31 15:44:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-31 15:44:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-31 15:44:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-31 15:44:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-31 15:44:13 [DEBUG] 已应用权限状态到UI控件
2025-07-31 15:44:13 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:44:14 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:14 [DEBUG] 授权控制器已初始化
2025-07-31 15:44:14 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:44:14 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:44:15 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:15 [DEBUG] 授权控制器已初始化
2025-07-31 15:44:15 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:44:15 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:44:16 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:16 [DEBUG] 授权控制器已初始化
2025-07-31 15:44:16 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:44:16 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:44:17 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:17 [DEBUG] 授权控制器已初始化
2025-07-31 15:44:17 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:44:17 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:44:17 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:17 [DEBUG] 授权控制器已初始化
2025-07-31 15:44:17 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:44:18 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:44:18 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:18 [DEBUG] 授权控制器已初始化
2025-07-31 15:44:18 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:44:18 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:44:19 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:19 [DEBUG] 授权控制器已初始化
2025-07-31 15:44:19 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-31 15:44:19 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-31 15:44:19 [DEBUG] 已重置工作表标签菜单
2025-07-31 15:44:19 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-31 15:44:25 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:25 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 88477062, 新父窗口: 2559652
2025-07-31 15:44:25 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2559652)
2025-07-31 15:44:25 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:25 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-07-31 15:44:25 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:25 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 2559652)
2025-07-31 15:44:25 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:25 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-31 15:44:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2559652
2025-07-31 15:44:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2559652)
2025-07-31 15:44:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:26 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-31 15:44:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2559652
2025-07-31 15:44:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2559652)
2025-07-31 15:44:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:27 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-31 15:44:27 [INFO] 系统事件监控已停止
2025-07-31 15:44:27 [INFO] Excel窗口句柄监控已停止
2025-07-31 15:44:27 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-31 15:44:27 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:27 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 88477062
2025-07-31 15:44:27 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:44:27 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 88477062
2025-07-31 15:44:27 [INFO] 系统事件监控已启动
2025-07-31 15:44:27 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:27 [INFO] App_WorkbookActivate: 工作簿 '☆51交付管理.xlsx' 激活处理完成
2025-07-31 15:44:27 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:27 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 88477062)
2025-07-31 15:44:27 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:27 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-31 15:44:28 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:28 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 88477062
2025-07-31 15:44:28 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:44:28 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:28 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-31 15:44:28 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:28 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 88477062
2025-07-31 15:44:28 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:44:28 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:29 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 88477062, 新父窗口: 9641060
2025-07-31 15:44:29 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9641060)
2025-07-31 15:44:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:29 [INFO] App_WorkbookActivate: 工作簿 '工作簿1' 激活处理完成
2025-07-31 15:44:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:29 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 9641060)
2025-07-31 15:44:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:29 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-31 15:44:29 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:29 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9641060
2025-07-31 15:44:29 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9641060)
2025-07-31 15:44:29 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:29 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-31 15:44:29 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 9641060
2025-07-31 15:44:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9641060)
2025-07-31 15:44:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:31 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-31 15:44:31 [INFO] 系统事件监控已停止
2025-07-31 15:44:31 [INFO] Excel窗口句柄监控已停止
2025-07-31 15:44:31 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-31 15:44:32 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 88477062
2025-07-31 15:44:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:44:32 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 88477062
2025-07-31 15:44:32 [INFO] 系统事件监控已启动
2025-07-31 15:44:32 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:32 [INFO] App_WorkbookActivate: 工作簿 '☆51交付管理.xlsx' 激活处理完成
2025-07-31 15:44:32 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-31 15:44:32 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 88477062)
2025-07-31 15:44:32 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-31 15:44:32 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-31 15:44:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 88477062
2025-07-31 15:44:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:44:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:32 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-31 15:44:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-31 15:44:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 88477062
2025-07-31 15:44:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 88477062)
2025-07-31 15:44:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-31 15:44:41 [DEBUG] 复选框初始化完成
2025-07-31 15:44:41 [INFO] 多边形GPS坐标转换器窗体初始化完成
2025-07-31 15:44:41 [INFO] OpenForm: 保持窗体现有标题 '多边形GPS坐标转换器'
2025-07-31 15:44:41 [INFO] OpenForm: 准备打开窗体 '多边形GPS坐标转换器'，位置: Center，单实例: True
2025-07-31 15:44:41 [INFO] 开始显示窗体 '多边形GPS坐标转换器'，位置模式: Center
2025-07-31 15:44:41 [INFO] 多边形GPS坐标转换器窗体加载完成
2025-07-31 15:44:42 [INFO] 窗体 '多边形GPS坐标转换器' 以TopMostForm为父窗体显示
2025-07-31 15:44:42 [INFO] 窗体 '多边形GPS坐标转换器' 显示完成，句柄: 5182896
2025-07-31 15:44:42 [INFO] OpenForm: 窗体 '多边形GPS坐标转换器' 打开成功
2025-07-31 15:44:45 [INFO] 多边形GPS坐标转换器窗体正在关闭
2025-07-31 15:44:51 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:44:51 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:44:51 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:44:51 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:44:51 [INFO] OpenForm: 保持窗体现有标题 '订单KML点图生成工具'
2025-07-31 15:44:51 [INFO] OpenForm: 准备打开窗体 '订单KML点图生成工具'，位置: Center，单实例: True
2025-07-31 15:44:51 [INFO] 开始显示窗体 '订单KML点图生成工具'，位置模式: Center
2025-07-31 15:44:51 [DEBUG] 配置设置绑定完成
2025-07-31 15:44:51 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:44:51 [INFO] [ET.Controls.ETLogDisplayControl] === 订单KML点图生成工具 ===
2025-07-31 15:44:51 [INFO] [ET.Controls.ETLogDisplayControl] 版本: v1.0
2025-07-31 15:44:51 [INFO] [ET.Controls.ETLogDisplayControl] 启动时间: 2025-07-31 15:44:51
2025-07-31 15:44:51 [INFO] [ET.Controls.ETLogDisplayControl] 
2025-07-31 15:44:51 [INFO] 订单KML点图生成工具已启动
2025-07-31 15:44:51 [INFO] [ET.Controls.ETLogDisplayControl] 欢迎使用订单KML点图生成工具！
2025-07-31 15:44:51 [INFO] [ET.Controls.ETLogDisplayControl] 请选择包含订单数据的Excel文件，然后设置KML输出路径。
2025-07-31 15:44:52 [INFO] 窗体 '订单KML点图生成工具' 以TopMostForm为父窗体显示
2025-07-31 15:44:52 [INFO] 窗体 '订单KML点图生成工具' 显示完成，句柄: 32314774
2025-07-31 15:44:52 [INFO] OpenForm: 窗体 '订单KML点图生成工具' 打开成功
2025-07-31 15:44:53 [INFO] OrderKmlGeneratorForm资源清理完成
2025-07-31 15:44:53 [INFO] 订单KML点图生成工具已关闭
2025-07-31 15:44:53 [INFO] OrderKmlGeneratorForm资源清理完成
2025-07-31 15:44:53 [INFO] OrderKmlGeneratorHelper正在释放资源
2025-07-31 15:44:53 [INFO] OrderDataExtractor正在释放资源
2025-07-31 15:44:53 [INFO] OrderDataExtractor资源释放完成
2025-07-31 15:44:53 [INFO] OrderKmlGeneratorHelper资源释放完成
2025-07-31 15:44:53 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-31 15:44:55 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:44:55 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:44:55 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:44:55 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:44:55 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-07-31 15:44:55 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:44:55 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:44:55 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:44:55 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:44:55 [INFO] [ET.Controls.ETLogDisplayControl] 站点数据转换器已就绪
2025-07-31 15:44:55 [INFO] [ET.Controls.ETLogDisplayControl] 功能说明：
2025-07-31 15:44:55 [INFO] [ET.Controls.ETLogDisplayControl] • 创建空白工作簿：自动创建包含"数据来源"和"数据输出"表的标准工作簿
2025-07-31 15:44:55 [INFO] [ET.Controls.ETLogDisplayControl] • 执行站点转换：将逻辑站点数据转换为物理站点汇总数据
2025-07-31 15:44:55 [INFO] [ET.Controls.ETLogDisplayControl] • 支持智能站点分组、动态频段配置和高性能GPS算法
2025-07-31 15:44:55 [INFO] [HyExcelVsto.Module.WX.StationConverter.StationConverterForm, Text: 站点系统数量统计] StationConverterForm初始化完成
2025-07-31 15:44:55 [INFO] OpenForm: 保持窗体现有标题 '站点系统数量统计'
2025-07-31 15:44:55 [INFO] OpenForm: 准备打开窗体 '站点系统数量统计'，位置: Center，单实例: False
2025-07-31 15:44:55 [INFO] 开始显示窗体 '站点系统数量统计'，位置模式: Center
2025-07-31 15:44:55 [INFO] 窗体 '站点系统数量统计' 以TopMostForm为父窗体显示
2025-07-31 15:44:55 [INFO] 窗体 '站点系统数量统计' 显示完成，句柄: 5639826
2025-07-31 15:44:55 [INFO] OpenForm: 窗体 '站点系统数量统计' 打开成功
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:44:58 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置Excel应用程序提供者：VSTOExcelApplicationProvider
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置Excel应用程序提供者：VSTOExcelApplicationProvider
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置Excel应用程序提供者：VSTOExcelApplicationProvider
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-07-31 15:44:58 [INFO] [ET.Controls.ETLogDisplayControl] 方向角/下倾角提取器已就绪
2025-07-31 15:44:58 [INFO] [ET.Controls.ETLogDisplayControl] 本功能：用于提取方向角和下倾角，并填写到指定列
注意：只有1小区的数据无法识别，该功能目前有Bug，不是很准确

使用方法：
1. 选择包含原始数据的来源范围
2. 选择方向角输出列（可选）
3. 选择下倾角输出列（可选）
4. 点击提取按钮执行操作

提取规则：
- 方向角：识别格式如 120/240/360 的数字组合
- 下倾角：识别范围在-5到20度之间的角度值
- 自动跳过筛选行和标题行
2025-07-31 15:44:58 [INFO] [HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm, Text: 方向角/下倾角提取器] 方向角提取器初始化完成
2025-07-31 15:44:58 [INFO] [HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm, Text: 方向角/下倾角提取器] AngleExtractorForm初始化完成
2025-07-31 15:44:58 [INFO] OpenForm: 保持窗体现有标题 '方向角/下倾角提取器'
2025-07-31 15:44:58 [INFO] OpenForm: 准备打开窗体 '方向角/下倾角提取器'，位置: Center，单实例: False
2025-07-31 15:44:58 [INFO] 开始显示窗体 '方向角/下倾角提取器'，位置模式: Center
2025-07-31 15:44:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 文本框获得焦点，上次地址：
2025-07-31 15:44:58 [INFO] 窗体 '方向角/下倾角提取器' 以TopMostForm为父窗体显示
2025-07-31 15:44:58 [INFO] 窗体 '方向角/下倾角提取器' 显示完成，句柄: 32380310
2025-07-31 15:44:58 [INFO] OpenForm: 窗体 '方向角/下倾角提取器' 打开成功
2025-07-31 15:44:59 [INFO] OpenForm: 保持窗体现有标题 'KML转换器 - 添加备注信息'
2025-07-31 15:44:59 [INFO] OpenForm: 准备打开窗体 'KML转换器 - 添加备注信息'，位置: Center，单实例: True
2025-07-31 15:44:59 [INFO] 开始显示窗体 'KML转换器 - 添加备注信息'，位置模式: Center
2025-07-31 15:45:00 [INFO] 窗体 'KML转换器 - 添加备注信息' 以TopMostForm为父窗体显示
2025-07-31 15:45:00 [INFO] 窗体 'KML转换器 - 添加备注信息' 显示完成，句柄: 33294822
2025-07-31 15:45:00 [INFO] OpenForm: 窗体 'KML转换器 - 添加备注信息' 打开成功
2025-07-31 15:45:02 [INFO] [HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm, Text: 方向角/下倾角提取器] AngleExtractorForm资源已释放
2025-07-31 15:45:02 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-07-31 15:45:04 [INFO] [HyExcelVsto.Module.WX.StationConverter.StationConverterForm, Text: 站点系统数量统计] StationConverterForm资源已释放
2025-07-31 15:45:04 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
