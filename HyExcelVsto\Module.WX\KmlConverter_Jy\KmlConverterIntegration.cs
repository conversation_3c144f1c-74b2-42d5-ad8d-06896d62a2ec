using System;
using System.Windows.Forms;
using ET;
using ExtensionsTools;

namespace HyExcelVsto.Module.WX.KmlConverter
{
    /// <summary>
    /// KML转换器集成类 提供与现有项目的集成接口
    /// </summary>
    public static class KmlConverterIntegration
    {
        /// <summary>
        /// 显示KML转换器窗体
        /// </summary>
        public static void ShowKmlConverter()
        {
            try
            {
                ETLogManager.Info("打开KML转换器窗体");

                using (frmKmlConverter form = new frmKmlConverter())
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"显示KML转换器窗体失败: {ex.Message}", ex);
                MessageBox.Show($"打开KML转换器失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 快速转换KML文件（通过对话框选择）
        /// </summary>
        /// <returns>转换是否成功</returns>
        public static bool QuickConvertKml()
        {
            try
            {
                ETLogManager.Info("启动快速KML转换");
                return KmlConverterHelper.ConvertKmlWithDialog();
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"快速转换KML失败: {ex.Message}", ex);
                MessageBox.Show($"快速转换失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 转换指定的KML文件
        /// </summary>
        /// <param name="sourceKmlPath">源KML文件路径</param>
        /// <param name="targetKmlPath">目标KML文件路径（可选，为空时自动生成）</param>
        /// <returns>转换是否成功</returns>
        public static bool ConvertSpecificKml(string sourceKmlPath, string targetKmlPath = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(sourceKmlPath))
                {
                    ETLogManager.Error("源KML文件路径不能为空");
                    return false;
                }

                // 如果没有指定目标路径，自动生成
                if (string.IsNullOrWhiteSpace(targetKmlPath))
                {
                    string directory = System.IO.Path.GetDirectoryName(sourceKmlPath);
                    string fileName = System.IO.Path.GetFileNameWithoutExtension(sourceKmlPath);
                    string extension = System.IO.Path.GetExtension(sourceKmlPath);
                    targetKmlPath = System.IO.Path.Combine(directory, $"{fileName}_converted{extension}");
                }

                ETLogManager.Info($"转换KML文件: {sourceKmlPath} -> {targetKmlPath}");

                // 执行转换
                bool success = KmlConverterHelper.ConvertKml(sourceKmlPath, targetKmlPath);

                if (success)
                {
                    ETLogManager.Info("KML文件转换成功");
                    MessageBox.Show($"KML文件转换成功！\n\n目标文件：{targetKmlPath}",
                                  "转换成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    ETLogManager.Error("KML文件转换失败");
                    MessageBox.Show("KML文件转换失败，请查看日志了解详细信息。",
                                  "转换失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                return success;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"转换指定KML文件失败: {ex.Message}", ex);
                MessageBox.Show($"转换失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 批量转换目录下的KML文件
        /// </summary>
        /// <param name="sourceDirectory">源目录路径（可选，为空时通过对话框选择）</param>
        /// <param name="targetDirectory">目标目录路径（可选，为空时通过对话框选择）</param>
        /// <returns>成功转换的文件数量</returns>
        public static int BatchConvertKmlFiles(string sourceDirectory = null, string targetDirectory = null)
        {
            try
            {
                // 如果没有指定源目录，通过对话框选择
                if (string.IsNullOrWhiteSpace(sourceDirectory))
                {
                    using (FolderBrowserDialog dialog = new FolderBrowserDialog())
                    {
                        dialog.Description = "选择包含KML文件的源目录";
                        if (dialog.ShowDialog() != DialogResult.OK)
                        {
                            return 0;
                        }
                        sourceDirectory = dialog.SelectedPath;
                    }
                }

                // 如果没有指定目标目录，通过对话框选择
                if (string.IsNullOrWhiteSpace(targetDirectory))
                {
                    using (FolderBrowserDialog dialog = new FolderBrowserDialog())
                    {
                        dialog.Description = "选择转换后文件的保存目录";
                        if (dialog.ShowDialog() != DialogResult.OK)
                        {
                            return 0;
                        }
                        targetDirectory = dialog.SelectedPath;
                    }
                }

                ETLogManager.Info($"批量转换KML文件: {sourceDirectory} -> {targetDirectory}");

                // 执行批量转换
                int successCount = KmlConverterHelper.BatchConvertKml(sourceDirectory, targetDirectory, true);

                ETLogManager.Info($"批量转换完成，成功转换 {successCount} 个文件");
                MessageBox.Show($"批量转换完成！\n\n成功转换 {successCount} 个文件",
                              "批量转换完成", MessageBoxButtons.OK, MessageBoxIcon.Information);

                return successCount;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"批量转换KML文件失败: {ex.Message}", ex);
                MessageBox.Show($"批量转换失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return 0;
            }
        }

        /// <summary>
        /// 验证KML文件格式
        /// </summary>
        /// <param name="kmlFilePath">KML文件路径</param>
        /// <returns>文件是否有效</returns>
        public static bool ValidateKmlFile(string kmlFilePath)
        {
            try
            {
                bool isValid = KmlConverterHelper.ValidateKmlFile(kmlFilePath);

                if (isValid)
                {
                    MessageBox.Show("KML文件格式有效", "验证结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("KML文件格式无效或存在问题", "验证结果", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"验证KML文件失败: {ex.Message}", ex);
                MessageBox.Show($"验证失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 显示转换效果示例
        /// </summary>
        public static void ShowConversionExample()
        {
            try
            {
                string exampleText = @"KML转换效果示例

转换前的地标：
名称: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)
坐标: 116.182524,23.306578,0
描述: <空>

转换后的地标：
名称: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)
坐标: 116.182524,23.306578,0
描述:
  第1行: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)
  第2行: 116.182524,23.306578
  第3行: 揭阳惠来大南海石化厂区西南搬迁

说明：
• 第1行：完整的地标名称
• 第2行：经纬度信息（经度,纬度，用半角逗号连接）
• 第3行：地标名第一个横杠(-)前的部分

使用效果：
在Google Earth等软件中点击地标图标时，会弹出备注界面显示上述信息";

                MessageBox.Show(exampleText, "转换效果示例", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"显示转换示例失败: {ex.Message}", ex);
                MessageBox.Show($"显示示例失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}