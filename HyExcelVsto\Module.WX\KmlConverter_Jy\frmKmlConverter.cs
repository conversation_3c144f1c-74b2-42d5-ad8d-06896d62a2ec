using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Xml.Linq;
using ET;
using ExtensionsTools;

namespace HyExcelVsto.Module.WX.KmlConverter
{
    /// <summary>
    /// KML转换器窗体 - 为KML文件中的地标添加备注信息
    /// </summary>
    public partial class frmKmlConverter : Form
    {
        public frmKmlConverter()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = "KML转换器 - 添加备注信息";
            this.Size = new System.Drawing.Size(600, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }

        /// <summary>
        /// 源文件文本框内容变化事件，自动生成目标文件名
        /// </summary>
        private void txtSourceFile_TextChanged(object sender, EventArgs e)
        {
            string filePath = txtSourceFile.Text.Trim();
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                // 自动生成目标文件名
                string sourceDir = Path.GetDirectoryName(filePath);
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                string extension = Path.GetExtension(filePath);
                txtTargetFile.Text = Path.Combine(sourceDir, $"{fileName}_converted{extension}");
            }
        }

        /// <summary>
        /// 源文件浏览按钮点击事件
        /// </summary>
        private void btnSourceBrowse_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openDialog = new OpenFileDialog())
            {
                openDialog.Title = "选择要转换的KML文件";
                openDialog.Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*";
                openDialog.FilterIndex = 1;

                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    txtSourceFile.Text = openDialog.FileName;
                }
            }
        }

        /// <summary>
        /// 目标文件浏览按钮点击事件
        /// </summary>
        private void btnTargetBrowse_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog saveDialog = new SaveFileDialog())
            {
                saveDialog.Title = "保存转换后的KML文件";
                saveDialog.Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*";
                saveDialog.FilterIndex = 1;
                saveDialog.DefaultExt = "kml";

                if (!string.IsNullOrEmpty(txtSourceFile.Text))
                {
                    string fileName = Path.GetFileNameWithoutExtension(txtSourceFile.Text);
                    saveDialog.FileName = $"{fileName}_converted.kml";
                }

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    txtTargetFile.Text = saveDialog.FileName;
                }
            }
        }

        /// <summary>
        /// 开始转换按钮点击事件
        /// </summary>
        private void btnConvert_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtSourceFile.Text))
                {
                    MessageBox.Show("请选择源KML文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtTargetFile.Text))
                {
                    MessageBox.Show("请指定目标文件路径", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!File.Exists(txtSourceFile.Text))
                {
                    MessageBox.Show("源文件不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 禁用按钮，显示进度
                btnConvert.Enabled = false;
                btnConvert.Text = "转换中...";
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 开始转换...\r\n");
                Application.DoEvents();

                // 执行转换
                bool success = ConvertKmlFile(txtSourceFile.Text, txtTargetFile.Text);

                if (success)
                {
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ✅ 转换成功！\r\n");
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 目标文件：{txtTargetFile.Text}\r\n");

                    MessageBox.Show($"KML文件转换成功！\n\n目标文件：{txtTargetFile.Text}",
                                  "转换成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 转换失败\r\n");
                    MessageBox.Show("转换失败，请查看日志了解详细信息", "转换失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 转换异常：{ex.Message}\r\n");
                MessageBox.Show($"转换时发生异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error($"转换KML文件异常: {ex.Message}", ex);
            }
            finally
            {
                // 恢复按钮状态
                btnConvert.Enabled = true;
                btnConvert.Text = "开始转换";
            }
        }

        /// <summary>
        /// 批量转换按钮点击事件
        /// </summary>
        private void btnBatchConvert_Click(object sender, EventArgs e)
        {
            try
            {
                using (FolderBrowserDialog sourceDialog = new FolderBrowserDialog())
                {
                    sourceDialog.Description = "选择包含KML文件的源目录";

                    if (sourceDialog.ShowDialog() != DialogResult.OK)
                        return;

                    using (FolderBrowserDialog targetDialog = new FolderBrowserDialog())
                    {
                        targetDialog.Description = "选择转换后文件的保存目录";

                        if (targetDialog.ShowDialog() != DialogResult.OK)
                            return;

                        // 禁用按钮
                        btnBatchConvert.Enabled = false;
                        btnBatchConvert.Text = "批量转换中...";
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 开始批量转换...\r\n");
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 源目录：{sourceDialog.SelectedPath}\r\n");
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 目标目录：{targetDialog.SelectedPath}\r\n");
                        Application.DoEvents();

                        // 执行批量转换
                        int successCount = BatchConvertKmlFiles(sourceDialog.SelectedPath, targetDialog.SelectedPath);

                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ✅ 批量转换完成，成功转换 {successCount} 个文件\r\n");
                        MessageBox.Show($"批量转换完成！\n\n成功转换 {successCount} 个文件",
                                      "批量转换完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 批量转换异常：{ex.Message}\r\n");
                MessageBox.Show($"批量转换时发生异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error($"批量转换KML文件异常: {ex.Message}", ex);
            }
            finally
            {
                // 恢复按钮状态
                btnBatchConvert.Enabled = true;
                btnBatchConvert.Text = "批量转换";
            }
        }

        /// <summary>
        /// 清空日志按钮点击事件
        /// </summary>
        private void btnClearLog_Click(object sender, EventArgs e)
        {
            txtLog.Clear();
        }

        /// <summary>
        /// 查看示例按钮点击事件
        /// </summary>
        private void btnShowExample_Click(object sender, EventArgs e)
        {
            try
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] === KML转换效果演示 ===\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] \r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 转换前的地标示例：\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 名称: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 坐标: 116.182524,23.306578,0\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 描述: <空>\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] \r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 转换后的地标示例：\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 名称: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 坐标: 116.182524,23.306578,0\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 描述:\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}]   第1行: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}]   第2行: 116.182524,23.306578\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}]   第3行: 揭阳惠来大南海石化厂区西南搬迁\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] \r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 说明：\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] • 第1行：完整的地标名称\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] • 第2行：经纬度信息（经度,纬度，用半角逗号连接）\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] • 第3行：地标名第一个横杠(-)前的部分\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] \r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 使用效果：在Google Earth等软件中点击地标图标时，会弹出备注界面显示上述信息\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ========================\r\n");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示示例时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error($"显示示例失败: {ex.Message}", ex);
            }
        }

        #region KML转换核心功能

        /// <summary>
        /// 转换KML文件，为地标添加备注信息
        /// </summary>
        /// <param name="sourceKmlPath">源KML文件路径</param>
        /// <param name="targetKmlPath">目标KML文件路径</param>
        /// <returns>转换是否成功</returns>
        private bool ConvertKmlFile(string sourceKmlPath, string targetKmlPath)
        {
            try
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 开始转换KML文件: {Path.GetFileName(sourceKmlPath)}\r\n");

                // 检查源文件是否存在
                if (!File.Exists(sourceKmlPath))
                {
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 源KML文件不存在: {sourceKmlPath}\r\n");
                    return false;
                }

                // 确保目标目录存在
                string targetDir = Path.GetDirectoryName(targetKmlPath);
                if (!string.IsNullOrEmpty(targetDir) && !Directory.Exists(targetDir))
                {
                    Directory.CreateDirectory(targetDir);
                }

                // 读取并解析KML文件
                XDocument kmlDoc = LoadKmlDocument(sourceKmlPath);
                if (kmlDoc == null)
                {
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 无法加载KML文档\r\n");
                    return false;
                }

                // 处理所有Placemark元素
                int processedCount = ProcessPlacemarks(kmlDoc);

                // 保存转换后的KML文件
                SaveKmlDocument(kmlDoc, targetKmlPath);

                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] KML转换完成，共处理 {processedCount} 个地标\r\n");
                return true;
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 转换KML文件时发生错误: {ex.Message}\r\n");
                ETLogManager.Error($"转换KML文件时发生错误: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 批量转换指定目录下的所有KML文件
        /// </summary>
        /// <param name="sourceDirectory">源目录路径</param>
        /// <param name="targetDirectory">目标目录路径</param>
        /// <returns>成功转换的文件数量</returns>
        private int BatchConvertKmlFiles(string sourceDirectory, string targetDirectory)
        {
            int successCount = 0;

            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(sourceDirectory) || !Directory.Exists(sourceDirectory))
                {
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 源目录不存在: {sourceDirectory}\r\n");
                    return 0;
                }

                if (string.IsNullOrWhiteSpace(targetDirectory))
                {
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 目标目录路径不能为空\r\n");
                    return 0;
                }

                // 确保目标目录存在
                if (!Directory.Exists(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                }

                // 获取所有KML文件
                string[] kmlFiles = Directory.GetFiles(sourceDirectory, "*.kml", SearchOption.TopDirectoryOnly);

                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 找到 {kmlFiles.Length} 个KML文件待转换\r\n");

                foreach (string sourceFile in kmlFiles)
                {
                    try
                    {
                        // 生成目标文件路径
                        string fileName = Path.GetFileNameWithoutExtension(sourceFile);
                        string extension = Path.GetExtension(sourceFile);
                        string targetFileName = $"{fileName}_converted{extension}";
                        string targetFile = Path.Combine(targetDirectory, targetFileName);

                        // 执行转换
                        if (ConvertKmlFile(sourceFile, targetFile))
                        {
                            successCount++;
                            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ✅ 成功转换: {Path.GetFileName(sourceFile)}\r\n");
                        }
                        else
                        {
                            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 转换失败: {Path.GetFileName(sourceFile)}\r\n");
                        }

                        Application.DoEvents(); // 保持界面响应
                    }
                    catch (Exception ex)
                    {
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 转换文件 {Path.GetFileName(sourceFile)} 时发生错误: {ex.Message}\r\n");
                        ETLogManager.Error($"转换文件 {Path.GetFileName(sourceFile)} 时发生错误: {ex.Message}", ex);
                    }
                }

                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 批量转换完成，成功转换 {successCount}/{kmlFiles.Length} 个文件\r\n");
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 批量转换KML文件失败: {ex.Message}\r\n");
                ETLogManager.Error($"批量转换KML文件失败: {ex.Message}", ex);
            }

            return successCount;
        }

        /// <summary>
        /// 加载KML文档，支持多种编码格式
        /// </summary>
        /// <param name="kmlFilePath">KML文件路径</param>
        /// <returns>XDocument对象</returns>
        private XDocument LoadKmlDocument(string kmlFilePath)
        {
            try
            {
                // 尝试不同的编码格式加载KML文件
                Encoding[] encodings = {
                    Encoding.UTF8,
                    Encoding.GetEncoding("GBK"),
                    Encoding.GetEncoding("GB2312"),
                    Encoding.GetEncoding("GB18030"),
                    Encoding.Default
                };

                foreach (Encoding encoding in encodings)
                {
                    try
                    {
                        // 使用指定编码读取文件内容
                        string xmlContent;
                        using (StreamReader reader = new StreamReader(kmlFilePath, encoding, true))
                        {
                            xmlContent = reader.ReadToEnd();
                        }

                        // 预处理XML内容
                        xmlContent = PreprocessXmlContent(xmlContent);

                        // 尝试解析为XDocument
                        XDocument kmlDoc = XDocument.Parse(xmlContent);
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 成功使用 {encoding.EncodingName} 编码加载KML文件\r\n");
                        return kmlDoc;
                    }
                    catch (Exception ex)
                    {
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 尝试使用 {encoding.EncodingName} 编码加载失败: {ex.Message}\r\n");
                        // 继续尝试下一种编码
                    }
                }

                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 无法使用任何编码格式加载KML文件\r\n");
                return null;
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 加载KML文档时发生错误: {ex.Message}\r\n");
                ETLogManager.Error($"加载KML文档时发生错误: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 预处理XML内容，解决常见的XML解析问题
        /// </summary>
        /// <param name="xmlContent">原始XML内容</param>
        /// <returns>预处理后的XML内容</returns>
        private string PreprocessXmlContent(string xmlContent)
        {
            if (string.IsNullOrEmpty(xmlContent))
                return xmlContent;

            // 替换可能导致解析错误的特殊字符
            xmlContent = xmlContent.Replace("\uFFFD", string.Empty);

            // 替换ASCII扩展字符集的非法XML字符
            xmlContent = Regex.Replace(xmlContent, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F]", string.Empty);

            // 处理XML声明问题
            if (!xmlContent.TrimStart().StartsWith("<?xml"))
            {
                xmlContent = $"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n{xmlContent}";
            }

            return xmlContent;
        }

        /// <summary>
        /// 处理所有Placemark元素，添加描述信息
        /// </summary>
        /// <param name="kmlDoc">KML文档</param>
        /// <returns>处理的地标数量</returns>
        private int ProcessPlacemarks(XDocument kmlDoc)
        {
            int processedCount = 0;

            try
            {
                // 查找所有Placemark元素（不考虑命名空间）
                var placemarks = kmlDoc.Descendants().Where(e => e.Name.LocalName == "Placemark");

                foreach (XElement placemark in placemarks)
                {
                    try
                    {
                        // 获取地标名称（支持name和n标签）
                        XElement nameElement = placemark.Descendants().FirstOrDefault(e => e.Name.LocalName == "name" || e.Name.LocalName == "n");
                        string stationName = nameElement?.Value?.Trim() ?? "";

                        if (string.IsNullOrEmpty(stationName))
                        {
                            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 跳过没有名称的地标\r\n");
                            continue;
                        }

                        // 获取坐标信息
                        XElement coordinatesElement = placemark.Descendants().FirstOrDefault(e => e.Name.LocalName == "coordinates");
                        string coordinates = coordinatesElement?.Value?.Trim() ?? "";

                        if (string.IsNullOrEmpty(coordinates))
                        {
                            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 地标 {stationName} 没有坐标信息\r\n");
                            continue;
                        }

                        // 解析经纬度
                        string[] coordParts = coordinates.Split(',');
                        if (coordParts.Length < 2)
                        {
                            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 地标 {stationName} 坐标格式不正确: {coordinates}\r\n");
                            continue;
                        }

                        string longitude = coordParts[0].Trim();
                        string latitude = coordParts[1].Trim();

                        // 生成描述信息
                        string description = GenerateDescription(stationName, longitude, latitude);

                        // 更新或创建description元素
                        UpdateDescription(placemark, description);

                        processedCount++;
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 已处理地标: {stationName}\r\n");
                    }
                    catch (Exception ex)
                    {
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 处理地标时发生错误: {ex.Message}\r\n");
                        ETLogManager.Error($"处理地标时发生错误: {ex.Message}", ex);
                        // 继续处理下一个地标
                    }
                }
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 处理Placemark元素时发生错误: {ex.Message}\r\n");
                ETLogManager.Error($"处理Placemark元素时发生错误: {ex.Message}", ex);
            }

            return processedCount;
        }

        /// <summary>
        /// 生成地标的描述信息
        /// </summary>
        /// <param name="stationName">地标名称</param>
        /// <param name="longitude">经度</param>
        /// <param name="latitude">纬度</param>
        /// <returns>描述信息</returns>
        private string GenerateDescription(string stationName, string longitude, string latitude)
        {
            // 第1行：地标名称
            string line1 = stationName;

            // 第2行：经纬度信息（用半角逗号连接）
            string line2 = $"{longitude},{latitude}";

            // 第3行：地标名第一个横杠前面的名字
            string line3 = ExtractPrefixBeforeFirstDash(stationName);

            // 组合成描述信息
            return $"{line1}\n{line2}\n{line3}";
        }

        /// <summary>
        /// 提取地标名第一个横杠前面的名字
        /// </summary>
        /// <param name="stationName">地标名称</param>
        /// <returns>第一个横杠前的名字</returns>
        private string ExtractPrefixBeforeFirstDash(string stationName)
        {
            if (string.IsNullOrEmpty(stationName))
                return "";

            // 查找第一个横杠的位置
            int dashIndex = stationName.IndexOf('-');
            if (dashIndex > 0)
            {
                return stationName.Substring(0, dashIndex).Trim();
            }

            // 如果没有横杠，返回原名称
            return stationName;
        }

        /// <summary>
        /// 更新或创建地标的description元素
        /// </summary>
        /// <param name="placemark">地标元素</param>
        /// <param name="description">描述内容</param>
        private void UpdateDescription(XElement placemark, string description)
        {
            try
            {
                // 查找现有的description元素
                XElement descElement = placemark.Descendants().FirstOrDefault(e => e.Name.LocalName == "description");

                if (descElement != null)
                {
                    // 更新现有的description元素
                    descElement.Value = description;
                }
                else
                {
                    // 创建新的description元素
                    XNamespace ns = placemark.Name.Namespace;
                    XElement newDescElement = new XElement(ns + "description", description);

                    // 将description元素插入到name或n元素之后
                    XElement nameElement = placemark.Descendants().FirstOrDefault(e => e.Name.LocalName == "name" || e.Name.LocalName == "n");
                    if (nameElement != null)
                    {
                        nameElement.AddAfterSelf(newDescElement);
                    }
                    else
                    {
                        // 如果没有name或n元素，插入到placemark的开头
                        placemark.AddFirst(newDescElement);
                    }
                }
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 更新description元素时发生错误: {ex.Message}\r\n");
                ETLogManager.Error($"更新description元素时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存KML文档到文件
        /// </summary>
        /// <param name="kmlDoc">KML文档</param>
        /// <param name="targetPath">目标文件路径</param>
        private void SaveKmlDocument(XDocument kmlDoc, string targetPath)
        {
            try
            {
                // 使用UTF-8编码保存文件
                using (StreamWriter writer = new StreamWriter(targetPath, false, Encoding.UTF8))
                {
                    kmlDoc.Save(writer);
                }

                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] KML文件已保存到: {targetPath}\r\n");
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 保存KML文件时发生错误: {ex.Message}\r\n");
                ETLogManager.Error($"保存KML文件时发生错误: {ex.Message}", ex);
                throw;
            }
        }

        #endregion KML转换核心功能
    }
}