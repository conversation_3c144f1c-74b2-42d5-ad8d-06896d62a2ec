using System;
using System.IO;
using System.Windows.Forms;
using ET;
using ExtensionsTools;

namespace HyExcelVsto.Module.WX.KmlConverter
{
    /// <summary>
    /// KML转换器窗体
    /// </summary>
    public partial class frmKmlConverter : Form
    {
        public frmKmlConverter()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = "KML转换器 - 添加备注信息";
            this.Size = new System.Drawing.Size(600, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // 设置目标文件选择控件为保存模式
            ucTargetFileSelect.UseOpenFileDialog = false;

            // 绑定源文件选择事件，自动生成目标文件名
            ucSourceFileSelect.OnPathSelected += UcSourceFileSelect_OnPathSelected;
        }

        /// <summary>
        /// 源文件选择事件处理，自动生成目标文件名
        /// </summary>
        private void UcSourceFileSelect_OnPathSelected(string filePath)
        {
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                // 自动生成目标文件名
                string sourceDir = Path.GetDirectoryName(filePath);
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                string extension = Path.GetExtension(filePath);
                ucTargetFileSelect.Text = Path.Combine(sourceDir, $"{fileName}_converted{extension}");
            }
        }

        /// <summary>
        /// 开始转换按钮点击事件
        /// </summary>
        private void btnConvert_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(ucSourceFileSelect.Text))
                {
                    MessageBox.Show("请选择源KML文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(ucTargetFileSelect.Text))
                {
                    MessageBox.Show("请指定目标文件路径", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!File.Exists(ucSourceFileSelect.Text))
                {
                    MessageBox.Show("源文件不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 禁用按钮，显示进度
                btnConvert.Enabled = false;
                btnConvert.Text = "转换中...";
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 开始转换...\r\n");
                Application.DoEvents();

                // 执行转换
                bool success = KmlConverterHelper.ConvertKml(ucSourceFileSelect.Text, ucTargetFileSelect.Text);

                if (success)
                {
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ✅ 转换成功！\r\n");
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 目标文件：{ucTargetFileSelect.Text}\r\n");

                    MessageBox.Show($"KML文件转换成功！\n\n目标文件：{ucTargetFileSelect.Text}",
                                  "转换成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 转换失败\r\n");
                    MessageBox.Show("转换失败，请查看日志了解详细信息", "转换失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 转换异常：{ex.Message}\r\n");
                MessageBox.Show($"转换时发生异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error($"转换KML文件异常: {ex.Message}", ex);
            }
            finally
            {
                // 恢复按钮状态
                btnConvert.Enabled = true;
                btnConvert.Text = "开始转换";
            }
        }

        /// <summary>
        /// 批量转换按钮点击事件
        /// </summary>
        private void btnBatchConvert_Click(object sender, EventArgs e)
        {
            try
            {
                using (FolderBrowserDialog sourceDialog = new FolderBrowserDialog())
                {
                    sourceDialog.Description = "选择包含KML文件的源目录";

                    if (sourceDialog.ShowDialog() != DialogResult.OK)
                        return;

                    using (FolderBrowserDialog targetDialog = new FolderBrowserDialog())
                    {
                        targetDialog.Description = "选择转换后文件的保存目录";

                        if (targetDialog.ShowDialog() != DialogResult.OK)
                            return;

                        // 禁用按钮
                        btnBatchConvert.Enabled = false;
                        btnBatchConvert.Text = "批量转换中...";
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 开始批量转换...\r\n");
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 源目录：{sourceDialog.SelectedPath}\r\n");
                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 目标目录：{targetDialog.SelectedPath}\r\n");
                        Application.DoEvents();

                        // 执行批量转换
                        int successCount = KmlConverterHelper.BatchConvertKml(sourceDialog.SelectedPath, targetDialog.SelectedPath, true);

                        txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ✅ 批量转换完成，成功转换 {successCount} 个文件\r\n");
                        MessageBox.Show($"批量转换完成！\n\n成功转换 {successCount} 个文件",
                                      "批量转换完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ❌ 批量转换异常：{ex.Message}\r\n");
                MessageBox.Show($"批量转换时发生异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error($"批量转换KML文件异常: {ex.Message}", ex);
            }
            finally
            {
                // 恢复按钮状态
                btnBatchConvert.Enabled = true;
                btnBatchConvert.Text = "批量转换";
            }
        }

        /// <summary>
        /// 清空日志按钮点击事件
        /// </summary>
        private void btnClearLog_Click(object sender, EventArgs e)
        {
            txtLog.Clear();
        }

        /// <summary>
        /// 查看示例按钮点击事件
        /// </summary>
        private void btnShowExample_Click(object sender, EventArgs e)
        {
            try
            {
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] === KML转换效果演示 ===\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] \r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 转换前的地标示例：\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 名称: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 坐标: 116.182524,23.306578,0\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 描述: <空>\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] \r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 转换后的地标示例：\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 名称: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 坐标: 116.182524,23.306578,0\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 描述:\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}]   第1行: 揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}]   第2行: 116.182524,23.306578\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}]   第3行: 揭阳惠来大南海石化厂区西南搬迁\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] \r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 说明：\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] • 第1行：完整的地标名称\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] • 第2行：经纬度信息（经度,纬度，用半角逗号连接）\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] • 第3行：地标名第一个横杠(-)前的部分\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] \r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] 使用效果：在Google Earth等软件中点击地标图标时，会弹出备注界面显示上述信息\r\n");
                txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] ========================\r\n");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示示例时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error($"显示示例失败: {ex.Message}", ex);
            }
        }
    }
}