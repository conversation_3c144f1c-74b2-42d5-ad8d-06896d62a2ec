# KML转换器 (重构版)

## 功能概述

KML转换器是一个简单易用的工具，用于为KML文件中的地标添加备注信息。转换后的KML文件在Google Earth等软件中点击地标图标时，会显示包含地标名称、经纬度和区域信息的备注界面。

**重构说明**：原本分散在4个文件中的功能现已整合到单个窗体文件中，简化了代码结构，提高了维护性，并解决了设计器错误问题。

## 主要功能

- **添加备注信息**：为KML文件中的每个地标添加3行备注信息
- **多编码支持**：自动检测和处理多种文件编码格式（UTF-8、GBK、GB2312等）
- **批量转换**：支持批量转换指定目录下的所有KML文件
- **简化界面**：使用标准Windows Forms控件，移除复杂的自定义控件依赖

## 备注信息格式

转换后，每个地标的备注信息包含3行：

1. **第1行**：完整的地标名称
2. **第2行**：经纬度信息（经度,纬度，用半角逗号连接）
3. **第3行**：地标名第一个横杠前的部分

### 示例

**原始地标**：
- 名称：`揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)`
- 坐标：`116.182524,23.306578,0`
- 描述：`<空>`

**转换后**：
- 名称：`揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)`
- 坐标：`116.182524,23.306578,0`
- 描述：
  ```
  揭阳惠来大南海石化厂区西南搬迁-铁塔-YD+LT+DX-(一体化机柜)
  116.182524,23.306578
  揭阳惠来大南海石化厂区西南搬迁
  ```

## 使用方法 (重构后)

### 1. 通过入口类调用

```csharp
using HyExcelVsto.Module.WX.KmlConverter;

// 显示KML转换器窗体
KmlConverterEntry.ShowKmlConverter();

// 快速转换（通过对话框选择文件）
bool success = KmlConverterEntry.QuickConvertKml();
```

### 2. 直接使用窗体

```csharp
using HyExcelVsto.Module.WX.KmlConverter;

// 直接创建和显示窗体
using (var form = new frmKmlConverter())
{
    form.ShowDialog();
}
```

### 3. 图形界面操作

1. **单文件转换**：
   - 点击源文件的"..."按钮选择KML文件
   - 目标文件路径会自动生成（可手动修改）
   - 点击"开始转换"按钮执行转换

2. **批量转换**：
   - 点击"批量转换"按钮
   - 选择包含KML文件的源目录
   - 选择转换后文件的保存目录
   - 系统自动处理目录下所有KML文件

3. **查看示例**：
   - 点击"查看示例"按钮了解转换效果

## 示例代码

查看 `Example/KmlConverterExample.cs` 文件获取完整的使用示例。

### 运行示例

```csharp
using HyExcelVsto.Module.WX.KmlConverter.Example;

// 基本转换示例
KmlConverterExample.BasicConversionExample();

// 对话框转换示例
KmlConverterExample.DialogConversionExample();

// 批量转换示例
KmlConverterExample.BatchConversionExample();

// 查看转换效果说明
KmlConverterExample.ShowConversionEffect();
```

## 技术特点

### 编码处理
- 自动检测文件编码（UTF-8、GBK、GB2312、GB18030等）
- 智能处理中文字符编码问题
- 支持BOM标记检测

### XML处理
- 兼容不同命名空间的KML文件
- 自动修复常见的XML格式问题
- 过滤非法XML字符

### 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 单个地标处理失败不影响其他地标

## 文件结构 (重构后)

```
KmlConverter_Jy/
├── frmKmlConverter.cs              # 主窗体逻辑 (包含所有转换功能)
├── frmKmlConverter.Designer.cs     # 窗体设计器代码 (使用标准控件)
├── frmKmlConverter.resx           # 窗体资源文件
├── KmlConverterEntry.cs           # 简化的入口调用类
└── README.md                      # 说明文档
```

### 重构改进
- ✅ **移除复杂依赖**：不再依赖 `ET.Controls.ETUcFileSelect` 自定义控件
- ✅ **代码整合**：将原本4个文件的功能整合到主窗体
- ✅ **简化界面**：使用标准的 `TextBox` 和 `Button` 控件，提高兼容性
- ✅ **单文件解决方案**：所有核心功能集中在一个文件中，便于维护

## 依赖项

- .NET Framework 4.7.2+
- System.Xml.Linq
- ExtensionsTools（日志管理）
- 标准 Windows Forms 控件

## 注意事项

1. **文件备份**：转换前建议备份原始KML文件
2. **编码问题**：如果遇到中文乱码，工具会自动尝试多种编码格式
3. **大文件处理**：对于包含大量地标的KML文件，转换可能需要一些时间
4. **文件权限**：确保对源文件有读取权限，对目标路径有写入权限

## 常见问题

### Q: 转换后的KML文件在Google Earth中显示乱码？
A: 这通常是编码问题。工具会自动使用UTF-8编码保存文件，如果仍有问题，请检查原始文件的编码格式。

### Q: 某些地标没有添加备注信息？
A: 可能是因为地标缺少名称或坐标信息。工具会跳过这些不完整的地标，并在日志中记录详细信息。

### Q: 如何查看详细的转换日志？
A: 工具使用ExtensionsTools的日志系统记录详细信息，可以通过ETLogManager查看日志。

## 文件说明

### 核心文件
- `KmlDescriptionConverter.cs` - 核心转换逻辑，处理KML文件解析和备注信息生成
- `KmlConverterHelper.cs` - 帮助类，提供便捷的转换接口和文件验证功能
- `KmlConverterIntegration.cs` - 集成类，提供与现有项目的集成接口

### 界面文件
- `frmKmlConverter.cs` - 主窗体，提供图形化的转换界面
- `frmKmlConverter.Designer.cs` - 窗体设计器文件

### 示例和测试
- `Example/KmlConverterExample.cs` - 使用示例代码
- `TestConverter.cs` - 测试转换功能
- `Program.cs` - 程序入口点，支持图形界面和命令行模式

## 快速开始

### 1. 在现有项目中集成

```csharp
using HyExcelVsto.Module.WX.KmlConverter;

// 显示转换器窗体
KmlConverterIntegration.ShowKmlConverter();

// 快速转换（通过对话框）
bool success = KmlConverterIntegration.QuickConvertKml();

// 转换指定文件
bool result = KmlConverterIntegration.ConvertSpecificKml(
    @"C:\path\to\source.kml",
    @"C:\path\to\target.kml");
```

### 2. 直接调用转换方法

```csharp
using HyExcelVsto.Module.WX.KmlConverter;

// 基本转换
string sourceKml = @"D:\HyDevelop\HyHelper\HyHelper\temp\【全量站】铁塔存量站址图层-20250515.kml";
string targetKml = @"D:\HyDevelop\HyHelper\HyHelper\temp\【全量站】铁塔存量站址图层-20250515_converted.kml";

bool success = KmlConverterHelper.ConvertKml(sourceKml, targetKml);
```

### 3. 运行测试

```csharp
using HyExcelVsto.Module.WX.KmlConverter;

// 运行快速测试
Program.QuickTest();

// 显示转换效果
TestConverter.ShowExample();
```

## 技术实现细节

### KML文件格式支持
- 支持标准的 `<name>` 标签
- 支持非标准的 `<n>` 标签（如提供的示例文件）
- 自动检测和处理不同的命名空间
- 兼容Google Earth和其他KML查看器

### 编码处理
- 自动检测文件编码（UTF-8、GBK、GB2312、GB18030等）
- 智能处理中文字符编码问题
- 支持BOM标记检测
- 输出文件使用UTF-8编码确保兼容性

### 错误处理
- 完善的异常处理机制
- 详细的日志记录（使用ExtensionsTools.ETLogManager）
- 单个地标处理失败不影响其他地标
- 用户友好的错误提示

## 更新日志

### v2.0.0 (重构版) - 2025-01-31
- 🔄 **重大重构**：将4个文件的功能整合到单个窗体文件
- ✅ **移除依赖**：不再依赖 `ET.Controls.ETUcFileSelect` 自定义控件
- ✅ **简化界面**：使用标准 Windows Forms 控件
- ✅ **提高稳定性**：解决了设计器错误问题
- ✅ **便于维护**：代码结构更加清晰简洁
- ✅ **保持功能**：所有原有功能完全保留

### v1.0.0 (2025-01-22)
- 初始版本发布
- 支持基本的KML转换功能
- 添加备注信息生成（3行格式）
- 支持多种编码格式自动检测
- 提供批量转换功能
- 支持标准name标签和非标准n标签
- 提供图形化界面和命令行接口
- 完整的集成接口和示例代码
